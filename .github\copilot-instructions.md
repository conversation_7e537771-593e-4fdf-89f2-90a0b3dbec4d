project-root/
├── src/
│   ├── app.js             # Main application file
│   ├── config/            # Configuration files
│   ├── plugins/           # Fastify plugins
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   ├── models/            # Data models
│   └── utils/             # Utility functions
├── prisma/
│   ├── schema.prisma      # Prisma schema
│   └── migrations/        # Database migrations
├── test/                  # Test files
├── .env                   # Environment variables
├── .gitignore
├── package.json
└── README.md