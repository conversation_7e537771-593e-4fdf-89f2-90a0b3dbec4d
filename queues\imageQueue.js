/**
 * Image generation queue for BullMQ
 * Handles image generation and variation tasks
 */

import { Queue } from "bullmq";
import { redisConfig } from "../config/redis.js";
import { defaultQueueOptions } from "./queueConfig.js";

// Queue configuration with specific settings for image generation
const queueOptions = {
  ...defaultQueueOptions,
  defaultJobOptions: {
    ...defaultQueueOptions.defaultJobOptions,
    attempts: 5, // Increased retry attempts for image generation
    timeout: 600000, // 10 minutes timeout for image generation
  },
  limiter: {
    max: 10, // Higher concurrency for image generation
    duration: 1000, // Per second
    groupKey: 'image-generation'
  }
};

// Create the queue
const imageQueue = new Queue('image-generation', queueOptions);

// Add error handling for the queue
imageQueue.on('error', (error) => {
  console.error('Image generation queue error:', error);
});

imageQueue.on('failed', (job, error) => {
  console.error(`Image generation job ${job.id} failed:`, error);
});

/**
 * Add an image generation job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} prompt - The prompt for image generation
 * @param {number} count - Number of images to generate
 * @param {string} size - Size of the images
 * @param {string} style - Style of the images
 * @returns {Promise<Job>} - The created job
 */
export async function addImageGenerationJob(taskId, prompt, count, size, style) {
  try {
    return await imageQueue.add('image-generation', {
      taskId,
      type: 'image-generation',
      prompt,
      count,
      size,
      style
    });
  } catch (error) {
    console.error('Error adding image generation job:', error);
    throw error;
  }
}

/**
 * Add an image variation job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} imageUrl - The URL of the image to create variations from
 * @param {number} count - Number of variations to generate
 * @param {number} variationStrength - Strength of the variation
 * @returns {Promise<Job>} - The created job
 */
export async function addImageVariationJob(taskId, imageUrl, count, variationStrength) {
  try {
    return await imageQueue.add('image-variation', {
      taskId,
      type: 'image-variation',
      imageUrl,
      count,
      variationStrength
    });
  } catch (error) {
    console.error('Error adding image variation job:', error);
    throw error;
  }
}

export default imageQueue;
