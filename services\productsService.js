import { INTERNAL_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js';
import { TwitterSnowflake } from "@sapphire/snowflake";
import { formatProductData } from '../scripts/productsUtils.js';

/**
 * Products Service
 * Contains business logic for product-related operations
 */
export const productsService = {
    /**
     * Get products list
     * @param {Object} params - Parameters
     * @param {number} params.page - Page number
     * @param {number} params.pageSize - Page size
     * @param {string} params.search - Search term
     * @param {string} params.type - Product type
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Products list and total count
     */
    async getProductsList({ page = 1, pageSize = 10, search, type, institutionId, fastify }) {
        try {
            // Convert page and pageSize to numbers
            const pageNum = Number(page);
            const pageSizeNum = Number(pageSize);
            const skip = (pageNum - 1) * pageSizeNum;
            const take = pageSizeNum;
            
            // Build where condition
            const where = {
                institutionId,
                ...(search ? { name: { contains: search, mode: 'insensitive' } } : {}),
                ...(type ? { packageType: type } : {})
            };
            
            // Get Prisma client
            const client = fastify.prisma;
            
            // Execute queries in parallel
            const [total, products] = await Promise.all([
                client.product.count({
                    where
                }),
                client.product.findMany({
                    where,
                    select: {
                        id: true,
                        name: true,
                        price: true,
                        leaveCount: true,
                        packageType: true,
                        usageLimit: true,
                        validTimeRange: true,
                        timeLimitedUsage: true,
                        timeLimitType: true,
                        remarks: true,
                        status: true,
                        createdAt: true
                    },
                    skip,
                    take,
                    orderBy: {
                        createdAt: 'desc'
                    }
                })
            ]);
            
            // Format product data
            const formattedProducts = products.map(product => formatProductData(product));
            
            // Return result
            return {
                list: formattedProducts,
                total,
                page: pageNum,
                pageSize: pageSizeNum
            };
        } catch (error) {
            throw new INTERNAL_ERROR(`获取产品列表失败: ${error.message}`);
        }
    },
    
    /**
     * Get all active products
     * @param {Object} params - Parameters
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Active products
     */
    async getAllActiveProducts({ institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Get active products
            const products = await client.product.findMany({
                where: {
                    institutionId,
                    status: 'active'
                },
                select: {
                    id: true,
                    name: true,
                    price: true,
                    leaveCount: true,
                    packageType: true,
                    usageLimit: true,
                    validTimeRange: true,
                    timeLimitedUsage: true,
                    timeLimitType: true
                }
            });
            
            // Format and return products
            return products.map(product => formatProductData(product));
        } catch (error) {
            throw new INTERNAL_ERROR(`获取产品失败: ${error.message}`);
        }
    },
    
    /**
     * Create product
     * @param {Object} params - Parameters
     * @param {Object} params.productData - Product data
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Created product
     */
    async createProduct({ productData, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Extract product data
            const {
                name, price, icon, cover, leaveCount, packageType, usageLimit,
                timeLimitedUsage, timeLimitType, validTimeRange, targetAudience,
                isShow, remarks, status = 'active'
            } = productData;
            
            // Generate product ID
            const id = TwitterSnowflake.generate().toString();
            
            // Create product
            const product = await client.product.create({
                data: {
                    id,
                    name,
                    price,
                    icon,
                    cover,
                    leaveCount,
                    packageType,
                    usageLimit,
                    timeLimitedUsage,
                    timeLimitType,
                    validTimeRange,
                    targetAudience,
                    isShow,
                    remarks,
                    status,
                    institutionId
                }
            });
            
            // Format and return product
            return formatProductData(product);
        } catch (error) {
            throw new INTERNAL_ERROR(`创建产品失败: ${error.message}`);
        }
    },
    
    /**
     * Update product
     * @param {Object} params - Parameters
     * @param {string} params.productId - Product ID
     * @param {Object} params.productData - Product data
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Updated product
     */
    async updateProduct({ productId, productData, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if product exists
            const existingProduct = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                }
            });
            
            if (!existingProduct) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Extract product data
            const {
                name, price, icon, cover, leaveCount, packageType, usageLimit,
                timeLimitedUsage, timeLimitType, validTimeRange, targetAudience,
                isShow, remarks, status
            } = productData;
            
            // Update product
            const updatedProduct = await client.product.update({
                where: {
                    id: productId
                },
                data: {
                    ...(name !== undefined && { name }),
                    ...(price !== undefined && { price }),
                    ...(icon !== undefined && { icon }),
                    ...(cover !== undefined && { cover }),
                    ...(leaveCount !== undefined && { leaveCount }),
                    ...(packageType !== undefined && { packageType }),
                    ...(usageLimit !== undefined && { usageLimit }),
                    ...(timeLimitedUsage !== undefined && { timeLimitedUsage }),
                    ...(timeLimitType !== undefined && { timeLimitType }),
                    ...(validTimeRange !== undefined && { validTimeRange }),
                    ...(targetAudience !== undefined && { targetAudience }),
                    ...(isShow !== undefined && { isShow }),
                    ...(remarks !== undefined && { remarks }),
                    ...(status !== undefined && { status })
                }
            });
            
            // Format and return updated product
            return formatProductData(updatedProduct);
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`更新产品失败: ${error.message}`);
        }
    },
    
    /**
     * Delete product
     * @param {Object} params - Parameters
     * @param {string} params.productId - Product ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Deleted product
     */
    async deleteProduct({ productId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if product exists
            const existingProduct = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                }
            });
            
            if (!existingProduct) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Check if product is used in student packages
            const studentPackagesCount = await client.studentPackage.count({
                where: {
                    productId,
                    institutionId
                }
            });
            
            if (studentPackagesCount > 0) {
                throw new VALIDATION_ERROR('产品已被学生购买，无法删除');
            }
            
            // Delete product-course associations
            await client.productCourse.deleteMany({
                where: {
                    productId
                }
            });
            
            // Delete product
            const deletedProduct = await client.product.delete({
                where: {
                    id: productId
                }
            });
            
            // Format and return deleted product
            return formatProductData(deletedProduct);
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`删除产品失败: ${error.message}`);
        }
    },
    
    /**
     * Get product courses
     * @param {Object} params - Parameters
     * @param {string} params.productId - Product ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Product courses
     */
    async getProductCourses({ productId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if product exists
            const existingProduct = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                }
            });
            
            if (!existingProduct) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Get product courses
            const productCourses = await client.productCourse.findMany({
                where: {
                    productId,
                    institutionId
                },
                include: {
                    course: {
                        select: {
                            id: true,
                            name: true,
                            type: true,
                            duration: true,
                            status: true
                        }
                    }
                }
            });
            
            // Format and return product courses
            return productCourses.map(pc => ({
                id: pc.id,
                courseId: pc.courseId,
                productId: pc.productId,
                course: pc.course
            }));
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`获取套餐绑定的课程失败: ${error.message}`);
        }
    },
    
    /**
     * Update product courses
     * @param {Object} params - Parameters
     * @param {string} params.productId - Product ID
     * @param {Array} params.courseIds - Course IDs
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async updateProductCourses({ productId, courseIds, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Check if product exists
            const existingProduct = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                }
            });
            
            if (!existingProduct) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Get existing product courses
            const existingProductCourses = await client.productCourse.findMany({
                where: {
                    productId,
                    institutionId
                }
            });
            
            // Find courses to add and remove
            const existingCourseIds = existingProductCourses.map(pc => pc.courseId);
            const coursesToAdd = courseIds.filter(id => !existingCourseIds.includes(id));
            const coursesToRemove = existingProductCourses.filter(pc => !courseIds.includes(pc.courseId));
            
            // Delete courses to remove
            if (coursesToRemove.length > 0) {
                await client.productCourse.deleteMany({
                    where: {
                        id: {
                            in: coursesToRemove.map(pc => pc.id)
                        }
                    }
                });
            }
            
            // Add new courses
            if (coursesToAdd.length > 0) {
                await client.productCourse.createMany({
                    data: coursesToAdd.map(courseId => ({
                        productId,
                        courseId,
                        institutionId
                    }))
                });
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`套餐绑定课程失败: ${error.message}`);
        }
    },
    
    /**
     * Get product by ID
     * @param {Object} params - Parameters
     * @param {string} params.productId - Product ID
     * @param {string} params.institutionId - Institution ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Product details
     */
    async getProductById({ productId, institutionId, fastify }) {
        try {
            // Get Prisma client
            const client = fastify.prisma;
            
            // Get product
            const product = await client.product.findFirst({
                where: {
                    id: productId,
                    institutionId
                },
                include: {
                    ProductCourse: {
                        include: {
                            course: {
                                select: {
                                    id: true,
                                    name: true,
                                    type: true,
                                    duration: true,
                                    status: true
                                }
                            }
                        }
                    }
                }
            });
            
            // Check if product exists
            if (!product) {
                throw new NOT_FOUND_ERROR('产品不存在');
            }
            
            // Format product data
            const formattedProduct = formatProductData(product);
            
            // Add courses
            formattedProduct.courses = product.ProductCourse.map(pc => pc.course);
            
            // Remove ProductCourse relation
            delete formattedProduct.ProductCourse;
            
            return formattedProduct;
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`获取产品详情失败: ${error.message}`);
        }
    }
};

export default productsService;
