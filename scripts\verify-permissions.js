import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 验证用户权限脚本
 * 检查各角色的权限分配是否正确
 */
async function verifyPermissions() {
    console.log('🔍 开始验证权限分配...\n');

    try {
        // 获取所有角色及其权限
        const roles = await prisma.role.findMany({
            include: {
                rolePermissions: {
                    include: {
                        permission: true
                    }
                }
            }
        });

        for (const role of roles) {
            console.log(`📋 角色: ${role.name} (${role.code})`);
            console.log(`权限数量: ${role.rolePermissions.length}`);
            
            // 按权限类别分组显示
            const permissionsByCategory = {};
            
            role.rolePermissions.forEach(rp => {
                const permission = rp.permission;
                const category = permission.code.split(':')[0];
                
                if (!permissionsByCategory[category]) {
                    permissionsByCategory[category] = [];
                }
                permissionsByCategory[category].push(permission.code);
            });

            // 显示各类别权限
            Object.keys(permissionsByCategory).sort().forEach(category => {
                console.log(`  ${category}: ${permissionsByCategory[category].length} 个权限`);
                permissionsByCategory[category].forEach(code => {
                    console.log(`    - ${code}`);
                });
            });
            
            console.log(''); // 空行分隔
        }

        // 特别检查机构管理员权限
        console.log('🎯 机构管理员权限详细检查:');
        const orgAdminRole = roles.find(r => r.code === 'orgadmin');
        
        if (orgAdminRole) {
            const permissions = orgAdminRole.rolePermissions.map(rp => rp.permission.code);
            
            // 检查关键权限
            const keyPermissions = [
                'system:access',
                'aitools:access',
                'notification:access',
                'academic:access',
                'finance:access',
                'data:access',
                'org:access'
            ];

            console.log('关键权限检查:');
            keyPermissions.forEach(perm => {
                const hasPermission = permissions.includes(perm);
                console.log(`  ${hasPermission ? '✅' : '❌'} ${perm}`);
            });

            // 检查系统管理子权限
            const systemPermissions = permissions.filter(p => p.startsWith('system:'));
            console.log(`\n系统管理权限 (${systemPermissions.length} 个):`);
            systemPermissions.forEach(perm => {
                console.log(`  ✅ ${perm}`);
            });

        } else {
            console.log('❌ 未找到机构管理员角色');
        }

        // 获取用户及其角色
        console.log('\n👥 用户角色分配:');
        const users = await prisma.user.findMany({
            where: {
                account: {
                    in: ['superadmin', 'orgadmin', 'teacher']
                }
            },
            include: {
                userRoles: {
                    include: {
                        role: true
                    }
                }
            }
        });

        users.forEach(user => {
            console.log(`用户: ${user.name} (${user.account})`);
            user.userRoles.forEach(ur => {
                console.log(`  角色: ${ur.role.name} (${ur.role.code})`);
            });
        });

    } catch (error) {
        console.error('❌ 权限验证失败:', error);
    } finally {
        await prisma.$disconnect();
    }
}

verifyPermissions();
