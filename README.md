# Cardmees API Backend

> 切记超级管理员为："SYSTEM_ADMIN"

This project is a high-performance API backend built with Fastify, featuring a well-organized architecture with dedicated workers and queues for background processing.

## Features

- High concurrency support with optimized performance
- Kubernetes deployment ready
- Docker containerization
- Horizontal scaling capabilities
- Database connection pooling
- Redis caching
- BullMQ for message queue processing
- Authentication and authorization
- API documentation with Swagger

## Project Structure

The project follows the MVC (Model-View-Controller) pattern with a clear separation of concerns:

- **controllers/**: Business logic handlers
- **routes/**: API endpoint definitions
- **services/**: Core business services
- **plugins/**: Fastify plugins for cross-cutting concerns
- **queues/**: BullMQ queue definitions
- **workers/**: BullMQ worker implementations
- **config/**: Configuration files
- **errors/**: Error handling
- **utils/**: Utility functions
- **prisma/**: Database schema and migrations

## Queue and Worker Architecture

The project uses BullMQ for asynchronous task processing with a clear separation between queues and workers:

### Queues
Located in the `queues/` folder, these define the job types and configurations:
- **imageQueue**: Handles image generation and variation tasks
- **textQueue**: Manages text generation tasks
- **speechQueue**: Processes text-to-speech and speech-to-text tasks
- **visionQueue**: Handles image analysis and captioning tasks

### Workers
Located in the `workers/` folder, these process the jobs from the queues:
- **imageWorker**: Processes image generation jobs
- **textWorker**: Processes text generation jobs
- **speechWorker**: Processes speech-related jobs
- **visionWorker**: Processes vision analysis jobs

## Prerequisites

- Node.js 20+
- Docker and Docker Compose
- Kubernetes cluster (for production deployment)
- PostgreSQL
- Redis

## Local Development

### Environment Setup

Create a `.env` file in the root directory with the following variables:

```
NODE_ENV=development
PORT=3000
POSTGRES_HOST=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=cardmees
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
JWT_SECRET=your_jwt_secret
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/cardmees?schema=public
```

### Install Dependencies

```bash
npm install
```

### Database Setup

```bash
npx prisma migrate dev
npm run seed:all
```

### Start Development Server

```bash
npm run dev
```

This will start both the API server and all workers.

### Running Specific Workers

You can run specific workers independently:

```bash
npm run worker:image    # Start only the image worker
npm run worker:text     # Start only the text worker
npm run worker:speech   # Start only the speech worker
npm run worker:vision   # Start only the vision worker
npm run worker:all      # Start all workers
```

## Docker Deployment

### Build and Run with Docker Compose

```bash
docker-compose up -d
```

This will start:
- API service
- Workers for all queues
- PostgreSQL database
- Redis cache

### Access the API

The API will be available at http://localhost:3000

## Kubernetes Deployment

### Prerequisites

- Kubernetes cluster
- kubectl configured to connect to your cluster
- Docker registry access

### Deployment Steps

1. Update the Docker registry in the deployment script:

```bash
export DOCKER_REGISTRY=your-registry.com
```

2. Run the deployment script:

```bash
chmod +x deploy.sh
./deploy.sh
```

3. Verify the deployment:

```bash
kubectl get pods -n cardmees
```

### Scaling

The application is configured with Horizontal Pod Autoscalers (HPA) that will automatically scale based on CPU and memory usage.

Initial configuration:
- API: 3-10 replicas
- Workers: 2-10 replicas

You can manually scale if needed:

```bash
kubectl scale deployment api-deployment -n cardmees --replicas=5
kubectl scale deployment workers-deployment -n cardmees --replicas=5
```

## Architecture

### Components

- **API Service**: Fastify-based REST API
- **Queue Workers**: Background processing for various AI tasks
- **PostgreSQL**: Primary database
- **Redis**: Caching and message queue

### High Concurrency Support

- Horizontal scaling with Kubernetes
- Connection pooling for database
- Redis caching for frequently accessed data
- BullMQ for asynchronous processing
- Optimized database queries
- Efficient error handling

## API Documentation

Swagger documentation is available at `/docs` endpoint.

## Monitoring

Health check endpoints:
- `/api/health`: Basic health check
- `/api/health/detailed`: Detailed health information (requires authentication)
- `/admin/queues`: BullMQ dashboard for monitoring queue status