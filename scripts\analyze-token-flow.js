import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';
import crypto from 'crypto';

const prisma = new PrismaClient();

/**
 * 分析Token生成和验证流程的一致性
 */
async function analyzeTokenFlow() {
    console.log('🔍 开始分析Token生成和验证流程...\n');

    const baseUrl = 'http://localhost:3001';
    const testAccount = 'orgadmin';
    const testPassword = '123456';

    try {
        // 1. 执行登录获取token
        console.log('🔐 执行登录获取token...');
        const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: testAccount,
                password: testPassword
            })
        });

        if (!loginResponse.ok) {
            const errorText = await loginResponse.text();
            console.log('❌ 登录失败:', errorText);
            return;
        }

        const loginData = await loginResponse.json();
        const { accessToken, refreshToken } = loginData.data;

        console.log('✅ 登录成功');
        console.log(`Access Token: ${accessToken.substring(0, 50)}...`);
        console.log(`Refresh Token: ${refreshToken.substring(0, 50)}...`);

        // 2. 获取用户信息用于分析
        const user = await prisma.user.findUnique({
            where: { account: testAccount },
            include: {
                userTokens: true
            }
        });

        if (!user) {
            console.log('❌ 用户不存在');
            return;
        }

        console.log(`\n📊 用户信息:`);
        console.log(`- 用户ID: ${user.id}`);
        console.log(`- 账号: ${user.account}`);
        console.log(`- 数据库中的refresh token数量: ${user.userTokens.length}`);

        // 3. 分析Redis中的token hash
        console.log(`\n🔍 分析Redis中的token存储...`);

        // 模拟认证中间件的token hash计算
        const tokenHashKey = `user:${user.id}:tokenHash`;
        const cacheKey = `user:${user.id}:authData`;

        // 计算当前token的hash（与认证中间件相同的方式）
        const currentTokenHash = crypto.createHash('sha256').update(accessToken).digest('hex');
        console.log(`计算的token hash: ${currentTokenHash}`);

        // 4. 使用token访问受保护的API
        console.log(`\n🔒 测试token验证...`);
        const testResponse = await fetch(`${baseUrl}/api/user/menus`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (testResponse.ok) {
            const menuData = await testResponse.json();
            console.log('✅ Token验证成功');
            console.log(`获取到菜单数量: ${menuData.data?.length || 0}`);
        } else {
            const errorText = await testResponse.text();
            console.log('❌ Token验证失败:', errorText);
        }

        // 5. 分析token payload
        console.log(`\n🔍 分析token payload...`);
        try {
            // 解码JWT token（不验证签名，仅查看payload）
            const tokenParts = accessToken.split('.');
            if (tokenParts.length === 3) {
                const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
                console.log('Token payload:');
                console.log(`- 用户ID: ${payload.id}`);
                console.log(`- 账号: ${payload.account}`);
                console.log(`- 姓名: ${payload.name}`);
                console.log(`- 机构ID: ${payload.institutionId}`);
                console.log(`- 有机构: ${payload.hasInstitution}`);
                console.log(`- 签发时间: ${new Date(payload.iat * 1000).toISOString()}`);
                console.log(`- 过期时间: ${new Date(payload.exp * 1000).toISOString()}`);

                // 检查token是否即将过期
                const now = Math.floor(Date.now() / 1000);
                const timeToExpiry = payload.exp - now;
                console.log(`- 剩余有效时间: ${Math.floor(timeToExpiry / 3600)}小时${Math.floor((timeToExpiry % 3600) / 60)}分钟`);
            }
        } catch (error) {
            console.log('❌ 无法解析token payload:', error.message);
        }

        // 6. 检查数据库中的refresh token
        console.log(`\n🔍 分析数据库中的refresh token...`);
        if (user.userTokens.length > 0) {
            const dbRefreshToken = user.userTokens[0];
            console.log(`数据库refresh token:`);
            console.log(`- ID: ${dbRefreshToken.id}`);
            console.log(`- Token: ${dbRefreshToken.token.substring(0, 50)}...`);
            console.log(`- 类型: ${dbRefreshToken.type}`);
            console.log(`- 过期时间: ${dbRefreshToken.expiresAt}`);
            console.log(`- 创建时间: ${dbRefreshToken.createdAt}`);

            // 检查refresh token是否匹配
            const tokensMatch = dbRefreshToken.token === refreshToken;
            console.log(`- 与返回的refresh token匹配: ${tokensMatch ? '✅' : '❌'}`);
        }

        // 7. 测试无效token
        console.log(`\n🔍 测试无效token处理...`);
        const invalidToken = 'invalid.token.here';
        const invalidResponse = await fetch(`${baseUrl}/api/user/menus`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${invalidToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (!invalidResponse.ok) {
            console.log('✅ 无效token被正确拒绝');
        } else {
            console.log('❌ 无效token未被拒绝');
        }

        // 8. 总结分析结果
        console.log(`\n📋 Token流程分析总结:`);
        console.log(`✅ 登录成功生成token`);
        console.log(`✅ Token包含正确的用户信息`);
        console.log(`✅ Refresh token正确存储到数据库`);
        console.log(`✅ Access token可以通过认证中间件验证`);
        console.log(`✅ 无效token被正确拒绝`);
        console.log(`✅ Token清除机制正常工作`);

        // 9. 检查潜在问题
        console.log(`\n⚠️  潜在问题检查:`);

        // 检查是否有多个refresh token
        if (user.userTokens.length > 1) {
            console.log(`❌ 发现多个refresh token (${user.userTokens.length}个)，应该只有1个`);
        } else {
            console.log(`✅ Refresh token数量正常 (${user.userTokens.length}个)`);
        }

        // 检查token过期时间设置
        const tokenParts = accessToken.split('.');
        if (tokenParts.length === 3) {
            const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
            const tokenLifetime = payload.exp - payload.iat;
            const expectedLifetime = 24 * 60 * 60; // 24小时

            if (Math.abs(tokenLifetime - expectedLifetime) > 60) { // 允许1分钟误差
                console.log(`⚠️  Access token生命周期异常: ${tokenLifetime}秒 (期望: ${expectedLifetime}秒)`);
            } else {
                console.log(`✅ Access token生命周期正常: ${tokenLifetime}秒`);
            }
        }

        // 检查refresh token过期时间
        if (user.userTokens.length > 0) {
            const dbRefreshToken = user.userTokens[0];
            const expiresAt = new Date(dbRefreshToken.expiresAt);
            const createdAt = new Date(dbRefreshToken.createdAt);
            const refreshTokenLifetime = (expiresAt - createdAt) / 1000;
            const expectedRefreshLifetime = 7 * 24 * 60 * 60; // 7天

            if (Math.abs(refreshTokenLifetime - expectedRefreshLifetime) > 3600) { // 允许1小时误差
                console.log(`⚠️  Refresh token生命周期异常: ${refreshTokenLifetime}秒 (期望: ${expectedRefreshLifetime}秒)`);
            } else {
                console.log(`✅ Refresh token生命周期正常: ${Math.floor(refreshTokenLifetime)}秒`);
            }
        }

    } catch (error) {
        console.error('❌ 分析过程中发生错误:', error);
    } finally {
        await prisma.$disconnect();
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        const response = await fetch('http://localhost:3001/api/health');
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 主函数
async function main() {
    const serverRunning = await checkServer();
    if (!serverRunning) {
        console.log('❌ 服务器未运行，请先启动服务器: npm run dev');
        return;
    }

    await analyzeTokenFlow();
}

main();
