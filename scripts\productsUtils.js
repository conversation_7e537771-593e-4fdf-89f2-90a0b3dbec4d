/**
 * Products Utility Functions
 * Contains utility functions for product-related operations
 */

/**
 * Format product data for response
 * @param {Object} productData - Product data from database
 * @returns {Object} Formatted product data
 */
export function formatProductData(productData) {
    if (!productData) return null;
    
    return {
        ...productData,
        createdAt: productData.createdAt ? Number(productData.createdAt) : null,
        updatedAt: productData.updatedAt ? Number(productData.updatedAt) : null,
        price: Number(productData.price || 0),
        leaveCount: Number(productData.leaveCount || 0),
        usageLimit: Number(productData.usageLimit || 0),
        timeLimitedUsage: Number(productData.timeLimitedUsage || 0)
    };
}

/**
 * Format product list for response
 * @param {Array} products - Array of product data from database
 * @returns {Array} Formatted product list
 */
export function formatProductList(products) {
    if (!products || !Array.isArray(products)) return [];
    
    return products.map(product => formatProductData(product));
}

/**
 * Get package type text
 * @param {string} packageType - Package type code
 * @returns {string} Package type text
 */
export function getPackageTypeText(packageType) {
    const packageTypeMap = {
        'class': '课时包',
        'membership': '会员卡',
        'course': '课程包',
        'special': '特价套餐'
    };
    
    return packageTypeMap[packageType] || packageType;
}

/**
 * Get product status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getProductStatusText(status) {
    const statusMap = {
        'active': '正常',
        'inactive': '停用',
        'deleted': '已删除'
    };
    
    return statusMap[status] || status;
}

/**
 * Get time limit type text
 * @param {string} timeLimitType - Time limit type code
 * @returns {string} Time limit type text
 */
export function getTimeLimitTypeText(timeLimitType) {
    const timeLimitTypeMap = {
        'day': '天',
        'week': '周',
        'month': '月',
        'year': '年'
    };
    
    return timeLimitTypeMap[timeLimitType] || timeLimitType;
}

/**
 * Get valid time range text
 * @param {string} validTimeRange - Valid time range code
 * @returns {string} Valid time range text
 */
export function getValidTimeRangeText(validTimeRange) {
    const validTimeRangeMap = {
        'purchase': '购买日算起',
        'first_use': '首次消费日算起'
    };
    
    return validTimeRangeMap[validTimeRange] || validTimeRange;
}

/**
 * Format price with currency symbol
 * @param {number} price - Price
 * @param {string} currency - Currency symbol
 * @returns {string} Formatted price
 */
export function formatPrice(price, currency = '¥') {
    if (price === undefined || price === null) return '';
    
    return `${currency}${Number(price).toFixed(2)}`;
}

/**
 * Calculate expiration date
 * @param {number} startDate - Start date timestamp
 * @param {number} timeLimitedUsage - Time limited usage
 * @param {string} timeLimitType - Time limit type
 * @returns {number} Expiration date timestamp
 */
export function calculateExpirationDate(startDate, timeLimitedUsage, timeLimitType) {
    if (!startDate || !timeLimitedUsage || !timeLimitType) return null;
    
    const date = new Date(startDate);
    
    switch (timeLimitType) {
        case 'day':
            date.setDate(date.getDate() + timeLimitedUsage);
            break;
        case 'week':
            date.setDate(date.getDate() + (timeLimitedUsage * 7));
            break;
        case 'month':
            date.setMonth(date.getMonth() + timeLimitedUsage);
            break;
        case 'year':
            date.setFullYear(date.getFullYear() + timeLimitedUsage);
            break;
        default:
            return null;
    }
    
    return date.getTime();
}

/**
 * Format expiration date
 * @param {number} expirationDate - Expiration date timestamp
 * @returns {string} Formatted expiration date
 */
export function formatExpirationDate(expirationDate) {
    if (!expirationDate) return '';
    
    const date = new Date(expirationDate);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

/**
 * Check if product is expired
 * @param {number} expirationDate - Expiration date timestamp
 * @returns {boolean} Whether the product is expired
 */
export function isProductExpired(expirationDate) {
    if (!expirationDate) return false;
    
    const now = new Date().getTime();
    return now > expirationDate;
}

/**
 * Calculate remaining days
 * @param {number} expirationDate - Expiration date timestamp
 * @returns {number} Remaining days
 */
export function calculateRemainingDays(expirationDate) {
    if (!expirationDate) return 0;
    
    const now = new Date().getTime();
    const diff = expirationDate - now;
    
    if (diff <= 0) return 0;
    
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
}

/**
 * Filter products by criteria
 * @param {Array} products - Array of product data
 * @param {Object} criteria - Filter criteria
 * @returns {Array} Filtered product list
 */
export function filterProducts(products, criteria) {
    if (!products || !Array.isArray(products)) return [];
    if (!criteria) return products;
    
    return products.filter(product => {
        // Filter by name
        if (criteria.name && !product.name.toLowerCase().includes(criteria.name.toLowerCase())) {
            return false;
        }
        
        // Filter by package type
        if (criteria.packageType && product.packageType !== criteria.packageType) {
            return false;
        }
        
        // Filter by status
        if (criteria.status && product.status !== criteria.status) {
            return false;
        }
        
        // Filter by price range
        if (criteria.minPrice !== undefined && product.price < criteria.minPrice) {
            return false;
        }
        if (criteria.maxPrice !== undefined && product.price > criteria.maxPrice) {
            return false;
        }
        
        // Filter by leave count range
        if (criteria.minLeaveCount !== undefined && product.leaveCount < criteria.minLeaveCount) {
            return false;
        }
        if (criteria.maxLeaveCount !== undefined && product.leaveCount > criteria.maxLeaveCount) {
            return false;
        }
        
        return true;
    });
}

/**
 * Sort products by field
 * @param {Array} products - Array of product data
 * @param {string} field - Field to sort by
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted product list
 */
export function sortProducts(products, field, order = 'asc') {
    if (!products || !Array.isArray(products)) return [];
    if (!field) return products;
    
    return [...products].sort((a, b) => {
        let valueA = a[field];
        let valueB = b[field];
        
        // Handle string fields
        if (typeof valueA === 'string') {
            valueA = valueA.toLowerCase();
        }
        if (typeof valueB === 'string') {
            valueB = valueB.toLowerCase();
        }
        
        // Sort by the field
        if (valueA < valueB) return order === 'asc' ? -1 : 1;
        if (valueA > valueB) return order === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Group products by package type
 * @param {Array} products - Array of product data
 * @returns {Object} Grouped products
 */
export function groupProductsByType(products) {
    if (!products || !Array.isArray(products)) return {};
    
    const groupedProducts = {};
    
    products.forEach(product => {
        const type = product.packageType || 'other';
        
        if (!groupedProducts[type]) {
            groupedProducts[type] = [];
        }
        
        groupedProducts[type].push(product);
    });
    
    return groupedProducts;
}

/**
 * Check if product has course
 * @param {Object} product - Product data
 * @param {string} courseId - Course ID
 * @returns {boolean} Whether the product has the course
 */
export function hasProductCourse(product, courseId) {
    if (!product || !product.courses || !Array.isArray(product.courses) || !courseId) {
        return false;
    }
    
    return product.courses.some(course => course.id === courseId);
}

export default {
    formatProductData,
    formatProductList,
    getPackageTypeText,
    getProductStatusText,
    getTimeLimitTypeText,
    getValidTimeRangeText,
    formatPrice,
    calculateExpirationDate,
    formatExpirationDate,
    isProductExpired,
    calculateRemainingDays,
    filterProducts,
    sortProducts,
    groupProductsByType,
    hasProductCourse
};
