/**
 * Finance model for database operations related to finance
 */
export default {
  /**
   * Get buying records with pagination and filtering
   */
  async getBuyingRecords(where, skip, take, orderBy) {
    const fastify = global.fastify;
    
    return Promise.all([
      fastify.prisma.studentProductRecord.findMany({
        where,
        select: {
          id: true,
          amount: true,
          amountPaid: true,
          amountUnpaid: true,
          giftCount: true,
          giftDays: true,
          paymentMethod: true,
          paymentTime: true,
          status: true,
          remarks: true,
          product: {
            select: {
              id: true,
              name: true,
            }
          },
          student: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
          salesRepresentative: {
            select: {
              id: true,
              name: true,
            },
          },
          operator: {
            select: {
              id: true,
              name: true,
            },
          },
          studentProduct: {
            select: {
              id: true,
              remainingBalance: true,
            }
          }
        },
        orderBy,
        skip,
        take,
      }),
      fastify.prisma.studentProductRecord.count({
        where,
      })
    ]);
  },

  /**
   * Get teaching records with filtering
   */
  async getTeachingRecords(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.classesSchedule.findMany({
      where,
      select: {
        id: true,
        teacherId: true,
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        StudentWeeklySchedule: {
          select: {
            status: true,
            operatorTime: true,
            attendanceCount: true,
            attendanceAmount: true,
          }
        }
      }
    });
  },

  /**
   * Get sales overview data
   */
  async getSalesOverviewData(where) {
    const fastify = global.fastify;
    
    return Promise.all([
      fastify.prisma.studentProductRefund.findMany({
        where,
        select: {
          status: true,
          amount: true,
          paymentTime: true,
        }
      }),
      fastify.prisma.studentProductRecord.findMany({
        where,
        select: {
          id: true,
          amount: true,
          amountPaid: true,
          amountUnpaid: true,
          paymentTime: true,
          status: true,
        }
      }),
    ]);
  },

  /**
   * Get salesrep records
   */
  async getSalesrepRecords(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProductRecord.findMany({
      where,
      select: {
        amount: true,
        amountPaid: true,
        amountUnpaid: true,
        paymentTime: true,
        status: true,
        salesRepresentative: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
  },

  /**
   * Get refund records
   */
  async getRefundRecords(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProductRefund.findMany({
      where,
      select: {
        amount: true,
        paymentTime: true,
        status: true,
        operator: {
          select: {
            name: true
          }
        }
      }
    });
  },

  /**
   * Get student overview data
   */
  async getStudentOverview(institutionId) {
    const fastify = global.fastify;
    
    return fastify.prisma.student.findMany({
      where: {
        institutionId
      },
      select: {
        birthday: true,
        gender: true,
        StudentProduct: {
          select: {
            StudentWeeklySchedule: {
              select: {
                classesSchedule: {
                  select: {
                    courses: {
                      select: {
                        name: true
                      }
                    }
                  }
                }
              }
            },
            product: {
              select: {
                name: true
              }
            }
          },
        }
      }
    });
  },

  /**
   * Get student count
   */
  async getStudentCount(institutionId) {
    const fastify = global.fastify;
    
    return fastify.prisma.student.count({
      where: { institutionId }
    });
  },

  /**
   * Get course count
   */
  async getCourseCount(institutionId) {
    const fastify = global.fastify;
    
    return fastify.prisma.course.count({
      where: { institutionId }
    });
  },

  /**
   * Count schedules
   */
  async countSchedules(institutionId, startDate, endDate, status = null) {
    const fastify = global.fastify;
    
    const where = {
      institutionId,
      classesSchedule: {
        startDate: {
          gte: startDate,
          lte: endDate
        }
      }
    };

    if (status) {
      where.status = status;
    }

    return fastify.prisma.studentWeeklySchedule.count({ where });
  },

  /**
   * Get classes schedule data
   */
  async getClassesScheduleData(institutionId, startDate, endDate) {
    const fastify = global.fastify;
    
    return fastify.prisma.classesSchedule.findMany({
      where: {
        institutionId,
        startDate: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        StudentWeeklySchedule: {
          select: {
            id: true,
          }
        },
        courses: {
          select: {
            name: true,
          }
        }
      }
    });
  },

  /**
   * Get student products count
   */
  async getStudentProductsCount(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProduct.count({
      where,
    });
  },

  /**
   * Get student products
   */
  async getStudentProducts(where, skip, take, orderBy) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProduct.findMany({
      where,
      select: {
        id: true,
        amount: true,
        totalCount: true,
        amountPaid: true,
        amountUnpaid: true,
        remainingCount: true,
        remainingAmount: true,
        payTime: true,
        type: true,
        remarks: true,
        product: {
          select: {
            id: true,
            name: true,
            packageType: true,
          }
        },
        student: {
          select: {
            id: true,
            name: true,
            phone: true,
          }
        },
      },
      skip, take, orderBy
    });
  },

  /**
   * Get all student products
   */
  async getAllStudentProducts(institutionId) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProduct.findMany({
      where: {
        institutionId,
      },
      select: {
        remainingCount: true,
        remainingAmount: true
      }
    });
  },

  /**
   * Get classes schedule with attendance
   */
  async getClassesScheduleWithAttendance(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.classesSchedule.findMany({
      where,
      select: {
        id: true,
        startDate: true,
        StudentWeeklySchedule: {
          select: {
            status: true,
            attendanceCount: true,
            attendanceAmount: true,
          }
        }
      }
    });
  },

  /**
   * Get refund records count
   */
  async getRefundRecordsCount(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProductRefund.count({ where });
  },

  /**
   * Get refund records
   */
  async getRefundRecords(where, skip, take) {
    const fastify = global.fastify;
    
    return fastify.prisma.studentProductRefund.findMany({
      where,
      select: {
        id: true,
        product: {
          select: {
            id: true,
            name: true
          }
        },
        student: {
          select: {
            id: true,
            name: true
          }
        },
        amount: true,
        reason: true,
        paymentTime: true,
        paymentMethod: true,
        operator: {
          select: {
            id: true,
            name: true
          }
        },
        status: true,
      },
      skip,
      take
    });
  },

  /**
   * Get bill count
   */
  async getBillCount(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.bill.count({ where });
  },

  /**
   * Get bills
   */
  async getBills(where) {
    const fastify = global.fastify;
    
    return fastify.prisma.bill.findMany({
      where,
      select: {
        amount: true,
        paymentTime: true,
        source: true,
        billType: true,
        status: true,
        paymentMethod: true,
      }
    });
  }
};
