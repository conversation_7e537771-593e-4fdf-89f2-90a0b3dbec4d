FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Production image, copy all the files and run the app
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 fastify

# Copy necessary files
COPY --from=builder --chown=fastify:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=fastify:nodejs /app/package.json ./package.json
COPY --from=builder --chown=fastify:nodejs /app/app.js ./app.js
COPY --from=builder --chown=fastify:nodejs /app/plugins ./plugins
COPY --from=builder --chown=fastify:nodejs /app/routes ./routes
COPY --from=builder --chown=fastify:nodejs /app/middleware ./middleware
COPY --from=builder --chown=fastify:nodejs /app/services ./services
COPY --from=builder --chown=fastify:nodejs /app/controllers ./controllers
COPY --from=builder --chown=fastify:nodejs /app/utils ./utils
COPY --from=builder --chown=fastify:nodejs /app/errors ./errors
COPY --from=builder --chown=fastify:nodejs /app/config ./config
COPY --from=builder --chown=fastify:nodejs /app/constants ./constants
COPY --from=builder --chown=fastify:nodejs /app/scripts ./scripts
COPY --from=builder --chown=fastify:nodejs /app/prisma ./prisma

# Switch to non-root user
USER fastify

# Expose the port the app will run on
EXPOSE 3000

# Command to run the application
CMD ["npm", "start"]
