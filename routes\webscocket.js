import { sendToAllUsersInInstitutionExceptUser } from "../libs/websocket.js";

export default async function (fastify, options) {
    // 创建通知的路由
    fastify.post('/create-notice', {
        schema: {
            body: {
                type: 'object',
                required: ['title', 'content'],
                properties: {
                    title: { type: 'string' },
                    content: { type: 'string' },
                },
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            try {
                const user = request.user;
                const { title, content } = request.body;
                
                const institutionId = user.institutionId;
                
                // 创建通知对象
                const notification = {
                    id: Date.now().toString(), // 临时ID
                    title,
                    content,
                    institutionId,
                    createdAt: new Date().toISOString(),
                    createdBy: user.id
                };
                const sendData = {
                    type: 'NOTIFICATION_ADD',
                    notification
                }
                
                // 发送通知给同一机构的所有用户

                await sendToAllUsersInInstitutionExceptUser(fastify, institutionId, user.id, sendData);
                
                return reply.status(200).send({
                    code: 1,
                    message: '创建成功',
                });
            } catch (error) {
                console.error(`创建通知处理失败: ${error.message}`);
                return reply.status(500).send({
                    code: 0,
                    message: '服务器错误',
                });
            }
        }
    });
    
    // 获取机构通知的路由
    fastify.get('/institution-notices/:institutionId', {
        schema: {
            params: {
                type: 'object',
                required: ['institutionId'],
                properties: {
                    institutionId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            try {
                const { institutionId } = request.params;
                const user = request.user;
                
                // 验证用户是否属于该机构
                const userInstitution = await fastify.prisma.userInstitution.findFirst({
                    where: {
                        userId: user.id,
                        institutionId: institutionId
                    }
                });
                
                if (!userInstitution) {
                    return reply.status(403).send({
                        code: 0,
                        message: '您不属于该机构',
                    });
                }
                
                // 获取机构通知
                const notices = await fastify.prisma.notification.findMany({
                    where: {
                        institutionId: institutionId
                    },
                    orderBy: {
                        createdAt: 'desc'
                    },
                    take: 50 // 限制返回数量
                });
                
                return reply.status(200).send({
                    code: 1,
                    message: '获取成功',
                    data: notices
                });
            } catch (error) {
                console.error(`获取机构通知失败: ${error.message}`);
                return reply.status(500).send({
                    code: 0,
                    message: '服务器错误',
                });
            }
        }
    });

    // WebSocket连接处理
    fastify.get('/ws', { websocket: true }, async (socket, request) => {
        // 处理连接
        const { token } = request.query;
        if (!token) {
            socket.close(1008, '缺少认证令牌');
            return;
        }

        // 验证token，查询数据库
        async function verifyToken(token) {
            try {
                const decoded = await fastify.jwt.verify(token);
                
                if (!decoded || !decoded.id) {
                    return null;
                }
                
                // 使用redis查询用户信息
                const userInfo = await fastify.redisWebSocket.get(`user:${decoded.id}`);
                let res;
                if (!userInfo) {
                    res = await fastify.pg.query(
                        'SELECT "userId", "institutionId" FROM user_institution WHERE "userId" = $1', 
                        [decoded.id]
                    );
                    if (!res.rows || res.rows.length === 0) {
                        return null;
                    }
                }

     
                

                
                return  userInfo ? userInfo : res.rows[0];
            } catch (error) {
                console.error(`Token验证失败: ${error.message}`);
                return null;
            }
        }

        try {
            const user = await verifyToken(token);
            
            if (!user) {
                socket.close(1008, '认证失败');
                return;
            }
            
            socket.instId = user.institutionId;
            socket.userId = user.userId;
            
            // 发送连接成功消息
            socket.send(JSON.stringify({
                type: 'CONNECT_SUCCESS',
                message: '连接成功',
            }));



            
            // 监听消息
            socket.on('message', async (message) => {
                try {
                    const data = JSON.parse(message);
                    // 处理不同类型的消息
                    if (data.type === 'ping') {
                        const sendData = JSON.stringify({
                            type: 'PONG',
                            timestamp: Date.now()
                        });
                        socket.send(sendData);
                    }
                    if (data.type === 'TASK_STATUS') {
                        const { taskId } = data;
                        const taskStatus = await fastify.redisTask.get(`task:${taskId}`);
                        socket.send(JSON.stringify({ type: 'TASK_STATUS', taskStatus }));
                    }
                } catch (error) {
                    console.error(`处理消息失败: ${error.message}`);
                }
            });
            
            // 监听连接关闭
            socket.on('close', async () => {
                try {
                   console.log(`${socket.id} 关闭连接`)
                   socket.close();
                } catch (error) {
                    console.error(`关闭连接处理失败: ${error.message}`);
                }
            });
            
            // 监听错误
            socket.on('error', (error) => {
                console.error(`WebSocket错误: ${error.message}`);
            });
            
        } catch (error) {
            console.error(`WebSocket连接处理失败: ${error.message}`);
            socket.close(1011, '服务器内部错误');
        }
    });

    fastify.get('/ws/list', {
        // onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const clients = fastify.websocketServer.clients;
            const list = [];
            for (const client of clients) {
                list.push({
                    instId: client.instId,
                    userId: client.userId
                })
            }
            return reply.status(200).send({
                code: 1,
                message: '获取成功',
                data: list
            });
        }
    });
}
