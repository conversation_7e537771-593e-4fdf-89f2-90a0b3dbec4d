/**
 * User Utility Functions
 * Contains utility functions for user-related operations
 */

/**
 * Format user data for response
 * @param {Object} userData - User data from database
 * @returns {Object} Formatted user data
 */
export function formatUserData(userData) {
    if (!userData) return null;
    
    return {
        ...userData,
        lastLoginAt: userData.lastLoginAt ? Number(userData.lastLoginAt) : null,
        createdAt: userData.createdAt ? Number(userData.createdAt) : null,
        updatedAt: userData.updatedAt ? Number(userData.updatedAt) : null,
        notificationCount: parseInt(userData.notificationCount || 0),
        roles: userData.roles || []
    };
}

/**
 * Format user list for response
 * @param {Array} users - Array of user data from database
 * @returns {Array} Formatted user list
 */
export function formatUserList(users) {
    if (!users || !Array.isArray(users)) return [];
    
    return users.map(user => formatUserData(user));
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {boolean} Whether the password is valid
 */
export function validatePassword(password) {
    if (!password) return false;
    
    // Password must be at least 8 characters long
    if (password.length < 8) return false;
    
    // Password must contain at least one letter and one number
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    
    return hasLetter && hasNumber;
}

/**
 * Generate random password
 * @returns {string} Random password
 */
export function generateRandomPassword() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let password = '';
    
    // Ensure at least one uppercase letter
    password += chars.charAt(Math.floor(Math.random() * 26) + 26);
    
    // Ensure at least one lowercase letter
    password += chars.charAt(Math.floor(Math.random() * 26));
    
    // Ensure at least one number
    password += chars.charAt(Math.floor(Math.random() * 10) + 52);
    
    // Add random characters to reach minimum length
    for (let i = 0; i < 5; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    // Shuffle the password
    return password.split('').sort(() => 0.5 - Math.random()).join('');
}

/**
 * Mask sensitive information
 * @param {Object} user - User data
 * @returns {Object} User data with masked sensitive information
 */
export function maskSensitiveInfo(user) {
    if (!user) return null;
    
    const maskedUser = { ...user };
    
    // Mask email
    if (maskedUser.email) {
        const [name, domain] = maskedUser.email.split('@');
        maskedUser.email = `${name.substring(0, 2)}***@${domain}`;
    }
    
    // Mask phone
    if (maskedUser.phone) {
        maskedUser.phone = maskedUser.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    
    return maskedUser;
}

/**
 * Get user status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getUserStatusText(status) {
    const statusMap = {
        'active': '正常',
        'inactive': '禁用',
        'deleted': '已删除'
    };
    
    return statusMap[status] || status;
}

/**
 * Check if user has permission
 * @param {Array} userPermissions - User permissions
 * @param {string} requiredPermission - Required permission
 * @returns {boolean} Whether the user has the permission
 */
export function hasPermission(userPermissions, requiredPermission) {
    if (!userPermissions || !Array.isArray(userPermissions) || !requiredPermission) {
        return false;
    }
    
    // Check for exact permission
    if (userPermissions.includes(requiredPermission)) {
        return true;
    }
    
    // Check for wildcard permission
    const parts = requiredPermission.split(':');
    for (let i = 1; i <= parts.length; i++) {
        const wildcardPermission = [...parts.slice(0, i), '*'].join(':');
        if (userPermissions.includes(wildcardPermission)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Check if user has any of the required permissions
 * @param {Array} userPermissions - User permissions
 * @param {Array} requiredPermissions - Required permissions
 * @returns {boolean} Whether the user has any of the permissions
 */
export function hasAnyPermission(userPermissions, requiredPermissions) {
    if (!userPermissions || !Array.isArray(userPermissions) || !requiredPermissions || !Array.isArray(requiredPermissions)) {
        return false;
    }
    
    return requiredPermissions.some(permission => hasPermission(userPermissions, permission));
}

/**
 * Check if user has all of the required permissions
 * @param {Array} userPermissions - User permissions
 * @param {Array} requiredPermissions - Required permissions
 * @returns {boolean} Whether the user has all of the permissions
 */
export function hasAllPermissions(userPermissions, requiredPermissions) {
    if (!userPermissions || !Array.isArray(userPermissions) || !requiredPermissions || !Array.isArray(requiredPermissions)) {
        return false;
    }
    
    return requiredPermissions.every(permission => hasPermission(userPermissions, permission));
}

/**
 * Check if user is admin
 * @param {Array} userRoles - User roles
 * @returns {boolean} Whether the user is admin
 */
export function isAdmin(userRoles) {
    if (!userRoles || !Array.isArray(userRoles)) {
        return false;
    }
    
    return userRoles.includes('admin') || userRoles.includes('超级管理员');
}

/**
 * Filter users by criteria
 * @param {Array} users - Array of user data
 * @param {Object} criteria - Filter criteria
 * @returns {Array} Filtered user list
 */
export function filterUsers(users, criteria) {
    if (!users || !Array.isArray(users)) return [];
    if (!criteria) return users;
    
    return users.filter(user => {
        // Filter by name
        if (criteria.name && !user.name.includes(criteria.name)) {
            return false;
        }
        
        // Filter by account
        if (criteria.account && !user.account.includes(criteria.account)) {
            return false;
        }
        
        // Filter by phone
        if (criteria.phone && (!user.phone || !user.phone.includes(criteria.phone))) {
            return false;
        }
        
        // Filter by email
        if (criteria.email && (!user.email || !user.email.includes(criteria.email))) {
            return false;
        }
        
        // Filter by status
        if (criteria.status && user.status !== criteria.status) {
            return false;
        }
        
        // Filter by role
        if (criteria.role && (!user.roles || !user.roles.includes(criteria.role))) {
            return false;
        }
        
        return true;
    });
}

export default {
    formatUserData,
    formatUserList,
    validatePassword,
    generateRandomPassword,
    maskSensitiveInfo,
    getUserStatusText,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin,
    filterUsers
};
