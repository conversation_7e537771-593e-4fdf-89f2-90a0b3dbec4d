import { uploadAvatar } from "../libs/qiniu.js";

export default async function (fastify) {
    fastify.post('/public/upload/image', {
        schema: {
            summary: '上传图片',
            consumes: ['multipart/form-data'],
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const data = await request.file();
            if (!data) {
              return reply.status(400).send({
                code: 400,
                message: '未找到上传的文件',
              });
            }

            // 获取文件类型
            const fileType = data.mimetype;
            if (!fileType.startsWith('image/')) {
                return reply.status(400).send({
                    code: 400,
                    message: '文件类型不支持',
                });
            }

            // 获取文件大小
            const fileSize = data.size;
            if (fileSize > 5 * 1024 * 1024) {
                return reply.status(400).send({
                    code: 400,
                    message: '文件大小超过5MB',
                });
            }
            const buffer = await data.toBuffer();
            try {
                const result = await uploadAvatar(buffer);
                return reply.success({
                    message: '上传成功',
                    data: result
                  });
              } catch (error) {
                console.error('文件上传错误:', error);
                return reply.status(500).send({
                    code: 500,
                    message: '文件上传失败',
                  });
              }
        

        }
    })
}