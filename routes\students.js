import { createError } from '@fastify/error';
import { TwitterSnowflake } from "@sapphire/snowflake"
import bcrypt from 'bcryptjs';
import { BASICCONSTANTS } from '../constants/index.js'

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

export default async function (fastify, opts) {
    // // 获取在读学员列表
    // fastify.get('/students', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生列表',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'string', default: 1 },
    //                 pageSize: { type: 'string', default: 10 },
    //                 search: { type: 'string' },
    //                 follower: { type: 'string' },
    //                 intention: { type: 'string' },
    //                 intentLevel: { type: 'string' },
    //                 type: {
    //                     type: 'string',
    //                     enum: ['formal', 'intent', 'public', 'graduated', 'all'],
    //                     default: 'formal'
    //                 },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async function (request, reply) {
    //         const institutionId = request.user.institutionId;
    //         const { page = '1', pageSize = 10, search, follower, intention, type, intentLevel } = request.query;

    //         // 解析分页参数
    //         const pageNum = parseInt(page, 10) || 1;
    //         const limit = parseInt(pageSize, 10) || 10;
    //         const skip = (pageNum - 1) * limit;


    //         try {
    //             // 查询学生数据

    //             const where = {
    //                 institutionId: institutionId,
    //                 ...(follower ? { followerId: follower } : {}),
    //                 ...(type !== 'all' ? { type: type } : {}),
    //                 ...(intentLevel ? { intentLevel: intentLevel } : {}),
    //             }

    //             if(search) {
    //                 where.OR = [
    //                     { name: { contains: search, mode: 'insensitive' }},
    //                     {phone: {contains: search, mode: 'insensitive'}},
    //                 ]
    //             }

    //             const [students, total] = await Promise.all([
    //                 fastify.prisma.student.findMany({
    //                     where,
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                         phone: true,
    //                         gender: true,
    //                         address: true,
    //                         remarks: true,
    //                         type: true,
    //                         intentLevel:true,

    //                         follower: {
    //                             select: {
    //                                 id: true,
    //                                 name: true,
    //                             }
    //                         },
    //                         followUpDate: true,
    //                         birthday: true,
    //                         status: true,
    //                         source: type === "public" || type === "intent" ? true : false,
    //                         sourceDesc: type === "public" || type === "intent" ? true : false,
    //                         createdAt: true,
    //                         // intention: true,
    //                     },
    //                     skip: skip,
    //                     take: limit,
    //                     orderBy: { createdAt: 'desc' } // 按创建时间排序
    //                 }),
    //                 fastify.prisma.student.count({
    //                     where
    //                 })
    //             ]);

    //             const transStudents = students.map((item) => ({
    //                 ...item,
    //                 birthday: item.birthday ? Number(item.birthday) : '',
    //                 followUpDate: item.followUpDate ? Number(item.followUpDate) : '',
    //                 createdAt: Number(item.createdAt),
    //                 followUpPerson: item.followUpPerson ? Number(item.followUpPerson) : '',
    //                 followUpDate: item.followUpDate ? Number(item.followUpDate) : '',


    //             }))

    //             reply.success({
    //                 data: {
    //                     list: transStudents,
    //                     total: total,
    //                     page: pageNum,
    //                     pageSize: pageSize,
    //                 },
    //                 message: '获取学生列表成功'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new fastify.httpErrors.InternalServerError(error.message || '获取学生列表失败');
    //         }
    //     }
    // })

    // //获取学员列表
    // fastify.get('/students/select', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员列表',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number' },
    //                 pageSize: { type: 'number' },
    //                 search: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const { page = 1, pageSize = 10, search } = request.query;
    //         const institutionId = request.user.institutionId;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;

    //         // 优化缓存键，使用哈希函数减少键长度
    //         const searchHash = search ? require('crypto').createHash('md5').update(search).digest('hex').substring(0, 8) : 'none';
    //         const redisKey = `students:${institutionId}:${searchHash}:${page}:${pageSize}`;

    //         // 缓存击穿保护
    //         const lockKey = `${redisKey}:lock`;

    //         try {
    //             // 使用弹性Redis工具从缓存获取数据
    //             const cachedResult = await fastify.redisResilience.resilientGet(
    //                 fastify.redis,
    //                 redisKey,
    //                 {
    //                     maxRetries: 2,
    //                     timeout: 3000,
    //                     logErrors: false
    //                 }
    //             );

    //             if(cachedResult) {
    //                 return reply.success({
    //                     data: JSON.parse(cachedResult),
    //                     message: '获取学员列表成功 (缓存)'
    //                 });
    //             }

    //             // 使用弹性Redis工具设置分布式锁防止缓存击穿
    //             const lock = await fastify.redisResilience.resilientSet(
    //                 fastify.redis,
    //                 lockKey,
    //                 '1',
    //                 'NX',
    //                 5,
    //                 { maxRetries: 1, timeout: 1000 }
    //             );

    //             // 如果无法获取锁，说明有其他请求正在处理，等待100ms后重试
    //             if (!lock) {
    //                 await new Promise(resolve => setTimeout(resolve, 100));
    //                 const retryCache = await fastify.redisResilience.resilientGet(
    //                     fastify.redis,
    //                     redisKey,
    //                     { timeout: 2000 }
    //                 );

    //                 if (retryCache) {
    //                     return reply.success({
    //                         data: JSON.parse(retryCache),
    //                         message: '获取学员列表成功 (重试缓存)'
    //                     });
    //                 }
    //             }

    //             // 构建查询条件
    //             const whereCondition = {
    //                 institutionId: institutionId,
    //                 ...(search ? {
    //                     OR: [
    //                         { name: { contains: search, mode: 'insensitive' } },
    //                         { phone: { contains: search, mode: 'insensitive' } }
    //                     ]
    //                 } : {})
    //             };

    //             // 并行执行查询
    //             const [result, total] = await Promise.all([
    //                 fastify.prisma.student.findMany({
    //                     where: whereCondition,
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                         phone: true,
    //                     },
    //                     skip: skip,
    //                     take: take,
    //                     orderBy: { createdAt: 'desc' } // 添加排序提高一致性
    //                 }),
    //                 fastify.prisma.student.count({
    //                     where: whereCondition
    //                 })
    //             ]);

    //             // 准备缓存数据
    //             const responseData = {
    //                 list: result,
    //                 total,
    //                 page,
    //                 pageSize
    //             };

    //             // 使用弹性Redis工具设置缓存，使用管道批处理
    //             await fastify.redisResilience.resilientMulti(
    //                 fastify.redis,
    //                 (multi) => {
    //                     multi.set(redisKey, JSON.stringify(responseData), 'EX', 300); // 缓存5分钟
    //                     multi.del(lockKey); // 释放锁
    //                 },
    //                 { timeout: 5000 },
    //                 async () => {
    //                     // 如果Redis操作失败，至少尝试释放锁
    //                     try {
    //                         await fastify.redisResilience.resilientDel(
    //                             fastify.redis,
    //                             lockKey,
    //                             { maxRetries: 1, timeout: 1000 }
    //                         );
    //                     } catch (e) {
    //                         fastify.log.error('Failed to release lock after Redis multi failure:', e);
    //                     }
    //                 }
    //             );

    //             // 返回响应
    //             return reply.success({
    //                 data: responseData,
    //                 message: '获取学员列表成功'
    //             });
    //         } catch (error) {
    //             // 出错时释放锁
    //             try {
    //                 await fastify.redisResilience.resilientDel(
    //                     fastify.redis,
    //                     lockKey,
    //                     { maxRetries: 1, timeout: 1000 }
    //                 );
    //             } catch (e) {
    //                 fastify.log.error('Failed to release lock after error:', e);
    //             }
    //             throw error;
    //         }
    //     }
    // })

    // // 获取学员上课记录
    // fastify.get('/students/classes-query', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员上课记录',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number' },
    //                 pageSize: { type: 'number' },
    //                 search: { type: 'string' },
    //                 studentId: { type: 'string' },
    //                 status: {
    //                     type: 'string',
    //                     enum: ['all', 'unattended', 'attendance', 'leave', 'absent'],
    //                     default: 'all'
    //                 },
    //                 startDate: { type: 'string' },
    //                 endDate: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const { page = 1, pageSize = 10, search, studentId, status, startDate, endDate } = request.query;
    //         const institutionId = request.user.institutionId;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;

    //         const where = {
    //             institutionId: institutionId,
    //             ...(status !== 'all' ? { status: status } : {}),
    //             ...(search ? {
    //                 student: {
    //                     OR: [
    //                         { name: { contains: search, mode: 'insensitive' } },
    //                         { phone: { contains: search, mode: 'insensitive' } }
    //                     ]
    //                 }
    //             } : {})
    //         }
    //         if(startDate && endDate) {
    //             where.classesSchedule = {
    //                 startDate: {
    //                     gte: startDate,
    //                     lte: endDate
    //                 }
    //             }
    //         }
    //         try {
    //             const [result, total] = await Promise.all([
    //                 fastify.prisma.studentWeeklySchedule.findMany({
    //                     where,
    //                     select: {
    //                         id: true,
    //                         status: true,
    //                         classesSchedule: {
    //                             select: {
    //                                 id: true,
    //                                 startDate: true,
    //                                 startTime: true,
    //                                 endTime: true,
    //                                 subject: true,
    //                                 teacher: {
    //                                     select: {
    //                                         id: true,
    //                                         name: true,
    //                                     }
    //                                 },
    //                                 courses: {
    //                                     select: {
    //                                         id: true,
    //                                         name: true,
    //                                     }
    //                                 },
    //                                 classes: {
    //                                     select: {
    //                                         id: true,
    //                                         name: true,
    //                                     }
    //                                 }
    //                             }
    //                         },
    //                         student: {
    //                             select: {
    //                                 id: true,
    //                                 name: true,
    //                             }
    //                         }
    //                     },
    //                     skip,
    //                     take,
    //                 }),
    //                 fastify.prisma.studentWeeklySchedule.count({
    //                     where,
    //                 })
    //             ])

    //             console.log(result);
    //             const transResult = result.map((item) => ({
    //                 ...item,
    //                 classesSchedule: {
    //                     ...item.classesSchedule,
    //                     startDate: Number(item.classesSchedule.startDate),
    //                 },

    //             }))
    //             reply.success({
    //                 data: {
    //                     list: transResult,
    //                     total,
    //                     page,
    //                     pageSize
    //                 }
    //             })

    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new fastify.httpErrors.InternalServerError(error.message || '获取学员上课记录失败');
    //         }
    //     }

    // })

    // // 学员创建跟进人
    // fastify.post('/students/follow-up', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '学员创建跟进人',
    //         body: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const { studentId } = request.body;
    //         const institutionId = request.user.institutionId;
    //         const followUpPersonId = request.user.id;


    //         const followUpDate = new Date().getTime();
    //         const updateResult = fastify.prisma.student.update({
    //             where: {
    //                 id: studentId,
    //                 institutionId: institutionId,
    //             },
    //             data: {
    //                 type: 'intent',
    //                 followerId: followUpPersonId,
    //                 followUpDate: followUpDate,
    //             }
    //         })


    //         const createResult = fastify.prisma.studentFollowRecords.create({
    //             data: {
    //                 studentId,
    //                 followUpDate: followUpDate,
    //                 followUpContent: '创建跟进人',
    //                 followUpUserId: followUpPersonId,
    //                 operatorId: followUpPersonId,
    //                 institutionId: institutionId,
    //             }
    //         })

    //         await Promise.all([updateResult, createResult]);

    //         reply.success({
    //             message: '创建跟进人成功.'
    //         })
    //     }
    // })

    // // 注册学生
    // fastify.post('/students/:institutionId/register', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '注册学生',
    //         params: {
    //             type: 'object',
    //             required: ['institutionId'],
    //             properties: {
    //                 institutionId: {
    //                     type: 'string'
    //                 }
    //             }
    //         },
    //         body: {
    //             type: 'object',
    //             required: ['name', 'phone', 'gender'],
    //             properties: {
    //                 name: { type: 'string' },
    //                 gender: { type: 'string' },
    //                 phone: { type: 'string' },
    //             },
    //         },
    //     },
    //     handler: async (request, reply) => {

    //         const institutionId = request.params.institutionId;
    //         const { name, gender, phone } = request.body;
    //         try {
    //             const institutionResult = await fastify.prisma.institution.findFirst({
    //                 where: {
    //                     id: institutionId
    //                 }
    //             })
    //             if (!institutionResult) {
    //                 throw new fastify.httpErrors.BadRequest('学校不存在');
    //             }
    //             const id = TwitterSnowflake.generate().toString();
    //             const password = await bcrypt.hash(BASICCONSTANTS.DEFAULT_PASSWORD, 10);
    //             const result = await fastify.prisma.student.create({
    //                 data: {
    //                     id,
    //                     name,
    //                     gender,
    //                     phone,
    //                     password,
    //                     institutionId,
    //                 }
    //             })
    //             reply.success({
    //                 data: result,
    //                 message: '创建学生成功'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             // throw new fastify.httpErrors.InternalServerError(error.message || '创建学生失败');
    //         }

    //     }
    // })


    // // 获取学员套餐列表
    // fastify.get('/students/products', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员套餐列表',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number' },
    //                 pageSize: { type: 'number' },
    //                 search: { type: 'string' },
    //                 remainingTimesMin: { type: 'number' },
    //                 remainingTimesMax: { type: 'number' },
    //                 remainingDaysMin: { type: 'number' },
    //                 remainingDaysMax: { type: 'number' },
    //                 status: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { page = 1, pageSize = 10, search, remainingTimesMin,
    //             remainingTimesMax, remainingDaysMin, remainingDaysMax, status
    //         } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;


    //         const where = {

    //             ...(remainingTimesMin ? { remainingSessionCount: { gte: remainingTimesMin } } : {}),
    //             ...(remainingTimesMax ? { remainingSessionCount: { lte: remainingTimesMax } } : {}),
    //             // ...(remainingDaysMin ? { remainingDays: { gte: remainingDaysMin } } : {}),
    //             // ...(remainingDaysMax ? { remainingDays: { lte: remainingDaysMax } } : {}),
    //             ...(status ? { enrollmentStatus: { equals: status } } : {}),
    //             institutionId: user.institutionId
    //         };
    //         if (search) {
    //             where.student = {
    //                 OR: [
    //                     { name: { contains: search, mode: 'insensitive' } },
    //                     { phone: { contains: search, mode: 'insensitive' } },
    //                 ]
    //             }
    //         }
    //         const [result, total] = await Promise.all([
    //             fastify.prisma.studentProduct.findMany({
    //                 where,
    //                 select: {
    //                     id: true,
    //                     startDate: true,
    //                     endDate: true,
    //                     totalSessionCount: true,
    //                     remainingSessionCount: true,
    //                     sessionUnitPrice: true,
    //                     enrollmentStatus: true,
    //                     remainingBalance: true,
    //                     product: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                             packageType: true,
    //                             usageLimit: true,
    //                             timeLimitType: true,
    //                             timeLimitedUsage: true,
    //                             validTimeRange: true
    //                         }
    //                     },

    //                     student: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                             phone: true,
    //                         }
    //                     }
    //                 },
    //                 orderBy: {
    //                     createdAt: 'desc'
    //                 },
    //                 skip,
    //                 take
    //             }),
    //             fastify.prisma.studentProduct.count({
    //                 where
    //             })
    //         ])


    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             startDate: Number(item.startDate),
    //             endDate: Number(item.endDate),
    //         }))
    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total: total,
    //                 page: page,
    //                 pageSize: pageSize
    //             },
    //             message: '获取学员套餐列表成功'
    //         });
    //     }
    // })
    // // 下载学员套餐列表
    // fastify.get('/students/products/download', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '下载学员套餐列表',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number' },
    //                 pageSize: { type: 'number' },
    //                 search: { type: 'string' },
    //                 remainingTimesMin: { type: 'number' },
    //                 remainingTimesMax: { type: 'number' },
    //                 remainingDaysMin: { type: 'number' },
    //                 remainingDaysMax: { type: 'number' },
    //                 status: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { page = 1, pageSize = 10, search, remainingTimesMin,
    //             remainingTimesMax, remainingDaysMin, remainingDaysMax, status
    //         } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;


    //         const where = {

    //             ...(remainingTimesMin ? { remainingCount: { gte: remainingTimesMin } } : {}),
    //             ...(remainingTimesMax ? { remainingCount: { lte: remainingTimesMax } } : {}),
    //             ...(remainingDaysMin ? { remainingDays: { gte: remainingDaysMin } } : {}),
    //             ...(remainingDaysMax ? { remainingDays: { lte: remainingDaysMax } } : {}),
    //             ...(status ? { status: { equals: status } } : {}),
    //             institutionId: user.institutionId
    //         };
    //         if (search) {
    //             where.student = {
    //                 OR: [
    //                     { name: { contains: search, mode: 'insensitive' } },
    //                     { phone: { contains: search, mode: 'insensitive' } },
    //                 ]
    //             }
    //         }
    //         const result = await fastify.prisma.studentProduct.findMany({
    //             where,
    //             select: {
    //                 id: true,
    //                 product: {
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                     }
    //                 },
    //                 student: {
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                         phone: true,
    //                     }
    //                 },
    //                 status: true,
    //                 remainingDays: true,
    //                 remainingCount: true,
    //             },
    //         })


    //         // const serializedResult = result.map(item => ({
    //         //     ...item,
    //         //     remainingDays: Number(item.remainingDays),
    //         //     remainingCount: Number(item.remainingCount),
    //         // }))
    //         reply.success({
    //             data: result,
    //             message: '获取学员套餐列表成功'
    //         });
    //     }
    // })

    // // 调整学生套餐内容
    // fastify.put('/students/products/:studentProductId', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '更新学生套餐内容',
    //         params: {
    //             type: 'object',
    //             required: ['studentProductId'],
    //             properties: {
    //                 studentProductId: { type: 'string' }
    //             }
    //         },
    //         body: {
    //             type: 'object',
    //             properties: {
    //                 remainingCount: { type: 'number', default: 0 },
    //                 remarks: { type: 'string', default: '' },
    //                 remainingDays: { type: 'number', default: 0 },
    //                 status: { type: 'string', default: 'active' },

    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const studentProductId = request.params.studentProductId;
    //         const { remainingCount, remarks, remainingDays, status } = request.body;
    //         try {
    //             const studentProduct = await fastify.prisma.studentProduct.findFirst({
    //                 where: { id: studentProductId, institutionId: user.institutionId },
    //                 select: {
    //                     id: true,
    //                     remainingSessionCount: true,
    //                     remainingBalance: true,
    //                     sessionUnitPrice: true,
    //                     totalSessionCount: true,
    //                     enrollmentStatus: true,
    //                     productId: true,
    //                     studentId: true,
    //                 }
    //             })
    //             if (!studentProduct) {
    //                 throw new fastify.httpErrors.BadRequest('学生套餐不存在');
    //             }

    //             // 更新套餐状态
    //             if (status && !remainingCount) {
    //                 await fastify.prisma.studentProduct.update({
    //                     where: { id: studentProductId, institutionId: user.institutionId },
    //                     data: { enrollmentStatus: status === studentProduct.enrollmentStatus ? 'active' : status }
    //                 })
    //                 reply.success({
    //                     message: '更新学生套餐内容成功.'
    //                 });
    //                 return;
    //             }

    //             // ============= 新 更新学生套餐内容 =============
    //                 // 1.计算增加数量
    //                 const addCount = Number.parseInt(remainingCount) - Number.parseInt(studentProduct.remainingSessionCount);

    //                 // 计算更新后的课时单价
    //                 const unitPrice = Number.parseFloat(studentProduct.remainingBalance / remainingCount).toFixed(3);
    //                 // 计算更新后的总课时
    //                 const totalCount = Number.parseInt(studentProduct.totalSessionCount) + Number.parseInt(addCount);
    //                 // 更新
    //                 const updateStudentProduct = fastify.prisma.studentProduct.update({
    //                     where: { id: studentProductId, institutionId: user.institutionId },
    //                     data: {
    //                         remainingSessionCount: remainingCount,
    //                         sessionUnitPrice: unitPrice,
    //                         totalSessionCount: totalCount
    //                     }
    //                 })

    //                 // 2.创建学生产品调整记录
    //                 const createStudentProductAdjust = fastify.prisma.studentProductAdjust.create({
    //                     data: {
    //                         id: TwitterSnowflake.generate().toString(),
    //                         studentProductId: studentProductId,
    //                         studentId: studentProduct.studentId,
    //                         productId: studentProduct.productId,
    //                         operatorTime: Number(new Date().getTime()),
    //                         type: addCount > 0 ? 'add' : 'reduce',
    //                         beforeCount: studentProduct.remainingSessionCount,
    //                         afterCount: remainingCount,
    //                         count: addCount > 0 ? addCount : -addCount,
    //                         operatorId: user.id,
    //                         institutionId: user.institutionId,
    //                         remarks: remarks,
    //                     }
    //                 })


    //                 // 3.创建操作日志
    //                 const createOperationLog = fastify.prisma.operationLog.create({
    //                     data: {
    //                         id: TwitterSnowflake.generate().toString(),
    //                         userId: user.id,
    //                         operationType: 'update',
    //                         content: `更新学生套餐内容成功. 更新前${studentProduct.remainingSessionCount} 更新课时: ${addCount > 0 ? '+' + addCount : addCount} 更新天数: ${remainingDays > 0 ? '+' + remainingDays : remainingDays}`,
    //                         describe: `更新学生套餐内容成功. 更新前${studentProduct.remainingSessionCount} 更新课时: ${addCount > 0 ? '+' + addCount : addCount} 更新天数: ${remainingDays > 0 ? '+' + remainingDays : remainingDays}`,
    //                         institutionId: user.institutionId,
    //                     }
    //                 })

    //                 // 4. 批量更新
    //                 await Promise.all([updateStudentProduct, createStudentProductAdjust, createOperationLog])


    //             // ============= 结束 更新学生套餐内容 =============

    //             // 20 - 30  -10

    //             // const newRemainingCount = Number.parseInt(remainingCount) - Number.parseInt(studentProduct.remainingCount);
    //             // const newRemainingDays = Number.parseInt(remainingDays) - Number.parseInt(studentProduct.remainingDays);
    //             // const totalCount = Number.parseInt(studentProduct.totalCount) + Number.parseInt(newRemainingCount);
    //             // const unitPrice = Number.parseFloat(studentProduct.amount / totalCount).toFixed(3);

    //             // const content = `更新学生套餐内容成功. 更新前${studentProduct.remainingCount} 更新课时: ${newRemainingCount > 0 ? '+' + newRemainingCount : newRemainingCount} 更新天数: ${newRemainingDays > 0 ? '+' + newRemainingDays : newRemainingDays}`
    //             // const describe = `更新学生套餐内容成功. 更新前${studentProduct.remainingCount} 更新课时: ${newRemainingCount > 0 ? '+' + newRemainingCount : newRemainingCount} 更新天数: ${newRemainingDays > 0 ? '+' + newRemainingDays : newRemainingDays}`
    //             // const result = await fastify.prisma.studentProduct.update({
    //             //     where: { id: studentProductId, institutionId: user.institutionId },
    //             //     data: {
    //             //         totalCount,
    //             //         unitPrice,
    //             //         remainingCount,
    //             //         remainingDays,
    //             //     }
    //             // })

    //             // await fastify.prisma.studentProductAdjust.create({
    //             //     data: {
    //             //         studentId: studentProduct.studentId,
    //             //         studentProductId: studentProductId,
    //             //         beforeCount: studentProduct.remainingCount,
    //             //         afterCount: remainingCount,
    //             //         beforeDays: studentProduct.remainingDays,
    //             //         afterDays: remainingDays,
    //             //         type: newRemainingCount >= 0 || newRemainingDays >= 0 ? 'add' : 'reduce',
    //             //         amount: unitPrice,
    //             //         count: newRemainingCount,
    //             //         days: newRemainingDays,
    //             //         operatorId: user.id,
    //             //         remarks: remarks,
    //             //         institutionId: user.institutionId,
    //             //         operatorTime: Number(new Date().getTime())
    //             //     }
    //             // })

    //             // await fastify.prisma.operationLog.create({
    //             //     data: {
    //             //         userId: user.id,
    //             //         operationType: 'update',
    //             //         content: content,
    //             //         describe: describe,
    //             //         institutionId: user.institutionId,
    //             //     }
    //             // })

    //             reply.success({
    //                 // data: result,
    //                 message: '更新学生套餐内容成功.'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             reply.code(500).send({ message: '更新学生套餐内容失败' });
    //         }
    //     }
    // })

    // // 机构添加学员
    // fastify.post('/students/add', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '添加学生',
    //         body: {
    //             type: 'object',
    //             required: ['name', 'phone'],
    //             properties: {
    //                 name: { type: 'string' },
    //                 gender: { type: 'string' },
    //                 phone: { type: 'string' },
    //                 birthday: { type: 'string' },
    //                 source: { type: 'string' },
    //                 sourceDesc: { type: 'string' },
    //                 referrer: { type: 'string' },
    //                 follower: { type: 'string' },
    //                 idCard: { type: 'string' },
    //                 address: { type: 'string' },
    //                 remarks: { type: 'string' },
    //                 intention: { type: 'string' },
    //                 type: { type: 'string', enum: ['formal', 'intent', 'public', 'graduated'], default: 'formal' },
    //             },
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const {
    //             name, gender, phone, birthday, source, sourceDesc, intention,
    //             referrer, follower, idCard, address, remarks, type
    //         } = request.body;
    //         try {
    //             const result = await fastify.prisma.student.create({
    //                 data: {
    //                     id: TwitterSnowflake.generate().toString(),
    //                     name,
    //                     phone,
    //                     gender,
    //                     birthday: Number(birthday),
    //                     sourceDesc,
    //                     intentLevel:intention,
    //                     followerId: follower,
    //                     source,
    //                     referrer,
    //                     address,
    //                     idCard,
    //                     remarks,
    //                     institutionId: user.institutionId,
    //                     type,
    //                     operatorId: user.id
    //                 }
    //             })
    //             reply.success({
    //                 // data: result,
    //                 message: '添加学生成功.'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '添加学生失败!');
    //         }
    //     }
    // })


    // // 创建学生产品 --- 新 等待更新
    // fastify.post('/students/:studentId/products', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '创建学生产品',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: {
    //                     type: 'string'
    //                 }
    //             }
    //         },
    //         body: {
    //             type: 'object',
    //             required: ['productId'],
    //             properties: {
    //                 productId: { type: 'string' },
    //                 amount: { type: 'number' },
    //                 prepaidAmount: { type: 'number' }, // 支付金额
    //                 balance: { type: 'number' }, // 为0时，表示全额支付
    //                 bonusLessons: { type: 'number' }, // 赠送课时
    //                 dateTime: { type: 'number' }, // 支付时间
    //                 remarks: { type: 'string', default: '' },
    //                 payment: { type: 'string' }, // 支付方式
    //                 salesRep: { type: 'string' }, // 销售代表
    //             },
    //         },
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const studentId = request.params.studentId;
    //         const { productId, amount, bonusLessons, dateTime, remarks,
    //             payment, salesRep, prepaidAmount } = request.body;
    //         try {
    //             const amountPaid = prepaidAmount
    //             const [productResult, studentResult] = await Promise.all([
    //                 fastify.prisma.product.findFirst({
    //                     where: {
    //                         id: productId,
    //                         institutionId: user.institutionId,
    //                         status: 'active'
    //                     }
    //                 }),
    //                 fastify.prisma.student.findFirst({
    //                     where: {
    //                         id: studentId,
    //                         institutionId: user.institutionId
    //                     }
    //                 })
    //             ])
    //             if (!productResult || !studentResult) {
    //                 throw new fastify.httpErrors.BadRequest('产品或学生不存在');
    //             }
    //             const id = TwitterSnowflake.generate().toString();
    //             const totalCount = productResult.usageLimit + Number(bonusLessons);
    //             const unitPrice = Number.parseFloat(Number(productResult.price) / Number(totalCount)).toFixed(3);


    //             // ============= 新创建学生产品 =============
    //             const data = {
    //                 id,
    //                 studentId,
    //                 productId,
    //                 totalSessionCount: Number(totalCount),
    //                 remainingSessionCount: Number(totalCount),
    //                 sessionUnitPrice: Number(unitPrice),
    //                 enrollmentStatus: 'active',
    //                 operatorId: user.id,
    //                 institutionId: user.institutionId,
    //             }
    //             // 根据套餐类型设置开始时间
    //             if(productResult.packageType === "limited-sessions"){
    //                 data.startDate = Number(new Date().getTime())
    //             }

    //             // 根据套餐有效时间范围设置开始时间与结束时间
    //             if(productResult.packageType === "purchase-date"){
    //                 data.startDate = Number(new Date().getTime())
    //                 // 根据套餐有效时间范围设置结束时间
    //                 if(productResult.timeLimitType === "daily"){
    //                     data.endDate = Number(new Date().getTime()) + productResult.timeLimitedUsage * 24 * 60 * 60 * 1000
    //                 }else if(productResult.timeLimitType === "monthly"){
    //                     // 使用date-fns 计算结束时间
    //                     data.endDate = addMonths(new Date(), productResult.timeLimitedUsage)
    //                 }

    //             }

    //             // 计算剩余金额
    //             const remainingBalance = Number(productResult.price) - Number(amountPaid)
    //             // 如果剩余金额小于0代表全额支付则设置套餐总结，否则设置支付金额
    //             data.remainingBalance = remainingBalance <= 0 ? productResult.price : amountPaid
    //             data.paymentStatus = remainingBalance <= 0 ? 'done' : 'arrears'

    //             const studentProduct = await fastify.prisma.studentProduct.create({
    //                 data
    //             })
    //             // 创建学生产品记录
    //             const createStudentProductRecord = fastify.prisma.studentProductRecord.create({
    //                 data: {
    //                     id: TwitterSnowflake.generate().toString(),
    //                     studentId,
    //                     productId,
    //                     studentProductId: id,
    //                     amount: productResult.price,
    //                     amountPaid: amountPaid,
    //                     amountUnpaid: remainingBalance,
    //                     purchaseQuantity: 1,
    //                     discount: 10,
    //                     giftCount: bonusLessons,
    //                     giftDays: 0,
    //                     paymentMethod: payment,
    //                     paymentTime: Number(dateTime),
    //                     salesRepresentativeId: salesRep,
    //                     operatorId: user.id,
    //                     status: 'done',
    //                     institutionId: user.institutionId,
    //                     remarks: remarks,
    //                 }
    //             })

    //             // 创建账单表
    //             const createBill = fastify.prisma.bill.create({
    //                 data: {
    //                     id: TwitterSnowflake.generate().toString(),
    //                     studentId,
    //                     productId,
    //                     amount: amountPaid,
    //                     paymentMethod: payment,
    //                     paymentTime: Number(dateTime),
    //                     billType: 'income',
    //                     source: 'productSales',
    //                     remarks: `${studentResult.name} 购买 ${productResult.name}`,
    //                     operatorId: user.id,
    //                     institutionId: user.institutionId,
    //                 }
    //             })

    //             // 创建操作日志
    //             const createOperationLog = fastify.prisma.operationLog.create({
    //                 data: {
    //                     id: TwitterSnowflake.generate().toString(),
    //                     userId: user.id,
    //                     operationType: 'create',
    //                     content: `为 ${studentResult.name} 创建套餐"${productResult.name}" 收款: ${amount} 赠送课时: ${bonusLessons}`,
    //                     describe: `为 ${studentResult.name} 创建套餐"${productResult.name}"`,
    //                     institutionId: user.institutionId,
    //                 }
    //             })

    //             // 创建学生产品调整记录
    //             const createStudentProductAdjustData = {
    //                 id: TwitterSnowflake.generate().toString(),
    //                 studentId,
    //                 studentProductId: id,
    //                 beforeCount: productResult.usageLimit,
    //                 afterCount: totalCount,
    //                 // giftCount: bonusLessons,
    //                 type: 'add',
    //                 operatorId: user.id,
    //                 operatorTime: Number(new Date().getTime()),
    //                 institutionId: user.institutionId,
    //             }
    //             if(productResult.packageType === "limited-time-and-count"){
    //                 // 等待计算赠送时长
    //             }
    //             const createStudentProductAdjust = fastify.prisma.studentProductAdjust.create({
    //                 data: createStudentProductAdjustData
    //             })

    //             await Promise.all([createStudentProductRecord, createBill,
    //                  createOperationLog, createStudentProductAdjust])


    //             // ============= 结束新创建学生产品 =============



    //             reply.success({
    //                 // data: serializedResult,
    //                 message: '创建学生产品成功'
    //             });
    //         } catch (error) {
    //             // fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '创建学生产品失败');
    //         } finally {

    //             // await fastify.prisma.$disconnect();
    //         }
    //     }
    // })
    // // 更新学生产品内容  -- 未使用
    // fastify.put('/students/:studentId/products/:studentProductId', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '更新学生产品内容',
    //         params: {
    //             type: 'object',
    //             required: ['studentId', 'studentProductId'],
    //             properties: {
    //                 studentId: {
    //                     type: 'string'
    //                 },
    //                 studentProductId: {
    //                     type: 'string'
    //                 }
    //             }
    //         },
    //         body: {
    //             type: 'object',
    //             // required: ['name', 'gender', 'phone'],
    //             properties: {
    //                 status: { type: 'string' },
    //                 totalCount: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const studentId = request.params.studentId;
    //         const studentProductId = request.params.studentProductId;
    //         const { status, totalCount } = request.body;
    //         try {
    //             let data = {}
    //             if (status) {
    //                 data.status = status
    //             }
    //             if (totalCount) {
    //                 data.totalCount = totalCount
    //             }

    //             // ============= 新 等待更新 =============

    //             let dt = {
    //                 ...(status ? { enrollmentStatus: status } : {}),
    //                 ...(totalCount ? { totalSessionCount: totalCount } : {}),
    //             }
    //             const studentProduct = await fastify.prisma.studentProduct.update({
    //                 where: {
    //                     id: studentProductId,
    //                     studentId,
    //                     institutionId: user.institutionId
    //                 },
    //                 data: dt
    //             })



    //             // ============= 结束新 等待更新 =============

    //             const result = await fastify.prisma.studentProduct.update({
    //                 where: {
    //                     id: studentProductId,
    //                     studentId,
    //                     institutionId: user.institutionId
    //                 },
    //                 data
    //             })
    //             reply.success({
    //                 data: result,
    //                 message: '更新学生产品状态成功'
    //             });
    //         } catch (error) {
    //             // fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '更新学生产品状态失败');
    //         }
    //     }
    // })

    // // 获取学生产品调整记录
    // fastify.get('/students/:studentId/products/adjustments', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生产品调整记录',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const result = await fastify.prisma.studentProductAdjust.findMany({
    //             where: {
    //                 studentId,
    //                 institutionId: request.user.institutionId
    //             }
    //         })
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             operatorTime: Number(item.operatorTime)
    //         }))
    //         reply.success({
    //             data: serializedResult,
    //             message: '获取学生产品调整记录成功'
    //         });
    //     }
    // })


    // // 获取意向学员列表
    // fastify.get('/students/intent', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取意向学员列表',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 // status: { type: 'string' },
    //                 search: { type: 'string' },
    //                 page: { type: 'number', default: 1 },
    //                 pageSize: { type: 'number', default: 10 }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { status, search, page, pageSize } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;

    //         const [result, total] = await Promise.all([
    //             fastify.prisma.student.findMany({
    //                 where: {
    //                     status: status ? { equals: status } : undefined,
    //                     name: search ? { contains: search } : undefined,
    //                     phone: search ? { contains: search } : undefined,
    //                     institutionId: user.institutionId,
    //                     type: 'intent'
    //                 },
    //                 select: {
    //                     id: true,
    //                     name: true,
    //                     phone: true,
    //                     gender: true,
    //                     birthday: true,
    //                     source: true,
    //                     sourceDesc: true,
    //                     followUpDate: true,
    //                     intentLevel: true,
    //                     type: true,
    //                     follower: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },


    //                 },
    //                 skip,
    //                 take
    //             }),
    //             fastify.prisma.student.count({
    //                 where: {
    //                     status: status ? { equals: status } : undefined,
    //                     name: search ? { contains: search } : undefined,
    //                     phone: search ? { contains: search } : undefined,
    //                     institutionId: user.institutionId,
    //                     type: 'intent'
    //                 }
    //             })
    //         ])
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             birthday: item.birthday ? Number(item.birthday) : null,
    //             followUpDate: item.followUpDate ? Number(item.followUpDate) : null,
    //         }))

    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total,
    //                 page,
    //                 pageSize
    //             },
    //             message: '获取意向学员列表成功'
    //         });
    //     }
    // })

    // // 学员添加跟进记录
    // fastify.post('/students/:studentId/followRecords', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '学员添加跟进记录',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         },
    //         body: {
    //             type: 'object',
    //             properties: {
    //                 nextFollowUpDate: { type: 'number' },
    //                 followUpDate: { type: 'number' },
    //                 followUpContent: { type: 'string' },
    //                 followUpUserId: { type: 'string' },
    //                 intentLevel: { type: 'string' },
    //                 // followUpResult: { type: 'string' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const studentId = request.params.studentId;
    //         const { followUpDate, followUpContent, nextFollowUpDate, intentLevel, followUpUserId } = request.body;
    //         try {

    //             if (intentLevel) {
    //                 await fastify.prisma.student.update({
    //                     where: {
    //                         id: studentId,
    //                         institutionId: user.institutionId
    //                     },
    //                     data: { intentLevel }
    //                 })
    //             }

    //             const result = await fastify.prisma.studentFollowRecords.create({
    //                 data: {
    //                     id: TwitterSnowflake.generate().toString(),
    //                     studentId,
    //                     followUpDate: followUpDate ? Number(followUpDate) : new Date().getTime(),
    //                     followUpContent,
    //                     followUpUserId: followUpUserId ? followUpUserId : user.id,
    //                     nextFollowUpDate: nextFollowUpDate ? Number(nextFollowUpDate) : null,
    //                     institutionId: user.institutionId,
    //                     operatorId: user.id,
    //                 }
    //             })
    //             reply.success({
    //                 // data: result,
    //                 message: '学员添加跟进记录成功'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '学员添加跟进记录失败');
    //         }
    //     }
    // })

    // // 获取学生跟进记录
    // fastify.get('/students/:studentId/followRecords', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生跟进记录',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const result = await fastify.prisma.studentFollowRecords.findMany({
    //             where: {
    //                 studentId,
    //                 institutionId: request.user.institutionId
    //             },
    //             select: {
    //                 id: true,
    //                 followUpDate: true,
    //                 nextFollowUpDate: true,
    //                 followUpContent: true,
    //                 // followUpUserId: true,
    //                 // operatorTime: true,
    //                 followUpUser: {
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                     }
    //                 },


    //             },
    //             orderBy: {
    //                 followUpDate: 'desc'
    //             }
    //         })
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             followUpDate: item.followUpDate ? Number(item.followUpDate) : null,
    //             nextFollowUpDate: item.nextFollowUpDate ? Number(item.nextFollowUpDate) : null,
    //             // operatorTime: Number(item.operatorTime)
    //         }))
    //         reply.success({
    //             data: serializedResult,
    //             message: '获取学生跟进记录成功'
    //         });
    //     }
    // })

    // // 获取单个学生(通过id，学校id(请求用户获取))
    // fastify.get('/students/:studentId', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生信息',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: {
    //                     type: 'string'
    //                 }
    //             }
    //         },
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         try {
    //             const result = await fastify.prisma.student.findFirst({
    //                 where: {
    //                     id: studentId,
    //                     institutionId: request.user.institutionId
    //                 },
    //                 include: {
    //                     password: false,
    //                     followUpDate: false,
    //                     followerId: false,
    //                     createdAt: false,
    //                     updatedAt: false,
    //                     institutionId: false,
    //                     operatorId: false
    //                 }
    //             })
    //             if (!result) {
    //                 throw new AUTH_ERROR('学生不存在');
    //             }
    //             const tran = {
    //                 ...result,
    //                 birthday: result.birthday ? Number(result.birthday) : null
    //             }
    //             reply.success({
    //                 data: tran,
    //                 message: '获取学生信息成功'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '获取学生信息失败');
    //             // throw new fastify.httpErrors.InternalServerError(error.message || '获取学生信息失败');
    //         }
    //     }
    // })
    // // 更新单个学生(通过id，学校id(请求用户获取))
    // fastify.put('/students/:studentId', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '更新学生信息',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: {
    //                     type: 'string'
    //                 }
    //             }
    //         },
    //         body: {
    //             type: 'object',
    //             // required: ['name', 'gender', 'phone'],
    //             properties: {
    //                 name: { type: 'string' },
    //                 gender: { type: 'string' },
    //                 age: { type: 'string' },
    //                 birthday: { type: 'string' },
    //                 phone: { type: 'string' },
    //                 email: { type: 'string' },
    //                 balance: { type: 'string' },
    //                 points: { type: 'string' },
    //                 followUpPerson: { type: 'string' },
    //                 followUpDate: { type: 'string' },
    //                 source: { type: 'string' },
    //                 referrer: { type: 'string' },
    //                 address: { type: 'string' },
    //                 idCard: { type: 'string' },
    //                 school: { type: 'string' },
    //                 intentionLevel: { type: 'string' },
    //                 parentName: { type: 'string' },
    //                 status: { type: 'string' },
    //                 type: { type: 'string' },
    //                 remark: { type: 'string' },
    //             },
    //         },
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const {
    //             name, gender, phone, age, birthday, email, balance, points,
    //             followUpPerson, followUpDate, source, referrer, address, idCard, school,
    //             intentionLevel, parentName, status, remark, type
    //         } = request.body;
    //         try {
    //             const result = await fastify.prisma.student.update({
    //                 where: {
    //                     id: studentId,
    //                     institutionId: request.user.institutionId
    //                 },
    //                 data: {
    //                     ...(name && { name }),
    //                     ...(gender && { gender }),
    //                     ...(phone && { phone }),
    //                     ...(age && { age }),
    //                     ...(birthday && { birthday }),
    //                     ...(email && { email }),
    //                     ...(balance && { balance }),
    //                     ...(points && { points }),
    //                     ...(followUpPerson && { followUpPerson }),
    //                     ...(followUpDate && { followUpDate }),
    //                     ...(source && { source }),
    //                     ...(referrer && { referrer }),
    //                     ...(address && { address }),
    //                     ...(idCard && { idCard }),
    //                     ...(school && { school }),
    //                     ...(intentionLevel && { intentionLevel }),
    //                     ...(parentName && { parentName }),
    //                     ...(status && { status }),
    //                     ...(remark && { remark }),
    //                     ...(type && { type }),
    //                 }
    //             })
    //             reply.success({
    //                 // data: result,
    //                 message: '更新学生信息成功'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '更新学生信息失败');
    //             // throw new fastify.httpErrors.InternalServerError(error.message || '更新学生信息失败');
    //         }
    //     }
    // })



    // // 批量删除学员
    // fastify.delete('/students', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '批量删除学员',
    //         body: {
    //             type: 'object',
    //             required: ['studentIds'],
    //             properties: {
    //                 studentIds: {
    //                     type: 'array',
    //                     items: { type: 'string' }
    //                 }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const { studentIds } = request.body;
    //         const user = request.user;
    //         try {
    //             const result = await fastify.prisma.student.deleteMany({
    //                 where: {
    //                     id: { in: studentIds },
    //                     institutionId: user.institutionId
    //                 }
    //             })


    //             reply.success({
    //                 message: '删除学员成功'
    //             });
    //             // 记录操作日志
    //             result.forEach(async (item) => {
    //                 await fastify.prisma.operationLog.create({
    //                     data: {
    //                         id: TwitterSnowflake.generate().toString(),
    //                         institutionId: user.institutionId,
    //                         userId: user.id,
    //                         content: `删除学员${item.name}`,
    //                         describe: `${new Date().toLocaleString()} 删除学员 ${item.name}`,
    //                         operationType: 'delete',
    //                     }
    //                 })
    //             })
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '删除学员失败');
    //         }
    //     }
    // })

    // // 获取学生上课记录
    // fastify.get('/students/:studentId/classesHistory', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生上课记录',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         },
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number', default: 1 },
    //                 pageSize: { type: 'number', default: 10 },
    //                 startDate: {
    //                     type: 'number',
    //                 },
    //                 endDate: {
    //                     type: 'number',
    //                 }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const user = request.user;
    //         const { page = 1, pageSize = 10, startDate, endDate } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;
    //         const where = {
    //             studentId,
    //             institutionId: user.institutionId,
    //             classesSchedule: {
    //                 startDate: { gte: startDate, lte: endDate }
    //             }
    //         }
    //         const [result, total] = await Promise.all([
    //             fastify.prisma.studentWeeklySchedule.findMany({
    //                 where,
    //                 select: {
    //                     id: true,
    //                     status: true,
    //                     // subject: true,
    //                     classesSchedule: {
    //                         select: {
    //                             id: true,
    //                             subject: true,
    //                             startDate: true,
    //                             startTime: true,
    //                             endTime: true,
    //                             classes: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             },
    //                             courses: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             },
    //                             teacher: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             }

    //                         }
    //                     }
    //                 },
    //                 orderBy: {
    //                     classesSchedule: {
    //                         startDate: 'desc'
    //                     }
    //                 },
    //                 skip,
    //                 take
    //             }),
    //             fastify.prisma.studentWeeklySchedule.count({
    //                 where
    //             })
    //         ])


    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             classesSchedule: {
    //                 ...item.classesSchedule,
    //                 startDate: Number(item.classesSchedule.startDate),
    //             }
    //         }))
    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total,
    //                 page,
    //                 pageSize
    //             },
    //             message: '获取学生上课记录成功'
    //         });
    //     }
    // })

    // // 获取学生购买套餐  -- 新 等待更新
    // fastify.get('/students/:studentId/products', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生购买套餐',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         },
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 status: {
    //                     type: 'string',
    //                     default: ''
    //                 }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const user = request.user;

    //         // ============= 新 获取学生购买套餐 =============
    //         const studentProduct = await fastify.prisma.studentProduct.findMany({
    //             where: {
    //                 studentId,
    //                 institutionId: user.institutionId
    //             },
    //             select: {
    //                 id: true,
    //                 startDate: true,
    //                 endDate: true,
    //                 totalSessionCount: true,
    //                 remainingSessionCount: true,
    //                 enrollmentStatus: true,
    //                 product: {
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                         packageType: true,
    //                     }
    //                 }

    //             }
    //         })
    //         const serializedStudentProduct = studentProduct.map(item => ({
    //             ...item,
    //             startDate: Number(item.startDate),
    //             endDate: Number(item.endDate),
    //             totalSessionCount: Number(item.totalSessionCount),
    //             remainingSessionCount: Number(item.remainingSessionCount),
    //         }))

    //         // ============= 结束 获取学生购买套餐 =============


    //         reply.success({
    //             data: serializedStudentProduct,
    //             message: '获取学生购买套餐成功'
    //         });
    //     }
    // })

    // // 获取学生购买记录  -- 新未更新
    // fastify.get('/students/:studentId/products/records', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学生购买记录',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const user = request.user;
    //         const { status } = request.query;


    //         // ============= 新获取学生购买记录 =============
    //         const select = {
    //             id: true,
    //             amount: true,
    //             amountPaid: true,
    //             amountUnpaid: true,
    //             paymentTime: true,
    //             paymentMethod: true,
    //             discount: true,
    //             giftCount: true,
    //             giftDays: true,
    //             purchaseQuantity: true,
    //             salesRepresentative: {
    //                 select: {
    //                     id: true,
    //                     name: true,
    //                 }
    //             },
    //             operator: {
    //                 select: {
    //                     id: true,
    //                     name: true,
    //                 }
    //             },
    //             product: {
    //                 select: {
    //                     id: true,
    //                     name: true,
    //                     packageType: true,
    //                 }
    //             },
    //             studentProduct: {
    //                 select: {
    //                     id: true,
    //                     startDate: true,
    //                     endDate: true,
    //                     remainingBalance:true,
    //                     totalSessionCount: true,
    //                     paymentStatus: true,
    //                 }
    //             }

    //         }

    //         const studentProductRecords = await fastify.prisma.studentProductRecord.findMany({
    //             where: {
    //                 studentId,
    //                 institutionId: user.institutionId
    //             },
    //             select
    //         })
    //         console.log(studentProductRecords,"studentProductRecords")

    //         const studentProductRecordResult = studentProductRecords.map(item => ({
    //             ...item,
    //             paymentTime: Number(item.paymentTime),
    //             studentProduct: {
    //                 ...item.studentProduct,
    //                 startDate:  Number(item.studentProduct?.startDate),
    //                 endDate: Number(item.studentProduct?.endDate),
    //             }
    //         }))

    //         // ============= 结束 获取学生购买记录 =============

    //         reply.success({
    //             data: studentProductRecordResult,
    //             message: '获取学生购买记录成功'
    //         });
    //     }
    // })

    // // 学员产品退款
    // fastify.post('/students/products/:productId/refund', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '学员产品退款',
    //         body: {
    //             type: 'object',
    //             properties: {
    //                 refundReason: { type: 'string', default: '' },
    //                 paymentMethod: { type: 'string', default: 'cash' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const productId = request.params.productId;
    //         const { refundReason, paymentMethod } = request.body;
    //         try {


    //             const [schedulesCount, studentProduct] = await Promise.all([
    //                 fastify.prisma.studentWeeklySchedule.count({
    //                     where: {
    //                         studentProductId: productId,
    //                         institutionId: user.institutionId
    //                     }
    //                 }),
    //                 fastify.prisma.studentProduct.findFirst({
    //                     where: {
    //                         id: productId,
    //                         institutionId: user.institutionId
    //                     },
    //                     select: {
    //                         id: true,
    //                         remainingBalance: true,
    //                         student: {
    //                             select: {
    //                                 id: true,
    //                                 name: true,
    //                             }
    //                         },
    //                         product: {
    //                             select: {
    //                                 id: true,
    //                                 name: true,
    //                             }
    //                         }
    //                     }
    //                 })
    //             ]);
    //             if (!studentProduct) {
    //                 return reply.code(404).send({ message: '未找到学员产品记录' });
    //               }






    //             await fastify.prisma.$transaction(async (tx) => {
    //                 if (!schedulesCount) {
    //                     await tx.studentProduct.delete({
    //                       where: {
    //                         id: productId,
    //                         institutionId: user.institutionId
    //                       }
    //                     });
    //                   } else {
    //                     await tx.studentProduct.update({
    //                       where: {
    //                         id: productId,
    //                         institutionId: user.institutionId
    //                       },
    //                       data: {
    //                         remainingSessionCount: 0,
    //                         remainingBalance: 0,
    //                         paymentStatus: 'refunded',
    //                         enrollmentStatus: 'completed'
    //                       }
    //                     });
    //                   }

    //                   const paymentTime = new Date().getTime();
    //                 // 修改学生产品记录状态
    //                 try {
    //                     // Try to find the record first
    //                     const studentProductRecord = await tx.studentProductRecord.findFirst({
    //                       where: {
    //                         studentProductId: productId,
    //                         studentId: studentProduct.student.id,
    //                         institutionId: user.institutionId
    //                       }
    //                     });

    //                     if (studentProductRecord) {
    //                       // If record exists, update it
    //                       await tx.studentProductRecord.update({
    //                         where: {
    //                           id: studentProductRecord.id,
    //                         },
    //                         data: { status: 'refunded' }
    //                       });
    //                     }
    //                   } catch (updateError) {
    //                     console.error(updateError)
    //                     fastify.log.warn('Failed to update studentProductRecord', {
    //                       error: updateError.message,
    //                       productId,
    //                       studentId: studentProduct.student.id
    //                     });
    //                   }

    //                 // 创建退款记录
    //                 const studentProductRefund =  tx.studentProductRefund.create({
    //                   data: {
    //                     id: TwitterSnowflake.generate().toString(),
    //                     studentId: studentProduct.student.id.toString(),
    //                     productId: studentProduct.product.id,
    //                     amount: studentProduct.remainingBalance,
    //                     reason: typeof refundReason === 'string' ? refundReason : String(refundReason),
    //                     paymentMethod,
    //                     operatorId: user.id,
    //                     paymentTime,
    //                     institutionId: user.institutionId
    //                   }
    //                 });

    //                 // 创建账单
    //                 const bill = tx.bill.create({
    //                     data: {
    //                         id: TwitterSnowflake.generate().toString(),
    //                         studentId: studentProduct.student.id.toString(),
    //                         productId: studentProduct.product.id,
    //                         amount: studentProduct.remainingBalance,
    //                         paymentTime,
    //                         operatorId: user.id,
    //                         institutionId: user.institutionId,
    //                         source: "refunded",
    //                         billType: 'expense',
    //                         remarks: `${studentProduct.student.name} 退款 ${studentProduct.product.name} 金额 ${studentProduct.remainingAmount}`,
    //                         // status: 'paid',
    //                         paymentMethod,
    //                     }
    //                 })
    //                 await Promise.all([ studentProductRefund, bill])

    //               });



    //               reply.success({ message: '学员产品退款成功' });



    //             // reply.success({
    //             //     message: '学员产品退款成功.'
    //             // });
    //         } catch (error) {
    //             fastify.log.error('退款处理失败', {
    //                 error: error.message,
    //                 stack: error.stack,
    //                 productId,
    //                 userId: user.id
    //               });

    //               // Handle specific errors
    //               if (error.code === 'P2025') {
    //                 return reply.code(404).send({ message: '记录不存在或已被删除' });
    //               } else if (error.code === 'P2002') {
    //                 return reply.code(409).send({ message: '记录冲突，可能已存在相同的记录' });
    //               } else if (error.code === 'P2003') {
    //                 return reply.code(400).send({ message: '关联记录不存在' });
    //               }


    //               reply.code(500).send({ message: '退款处理失败', error: process.env.NODE_ENV === 'development' ? error.message : undefined });
    //         }

    //     }
    // })

    // // 学员退出班级
    // fastify.delete('/students/:studentId/classes/:classesId', {
    //     schema: {
    //         tags: ['classes'],
    //         summary: '学员退班',
    //         params: {
    //             type: 'object',
    //             required: ['classesId', 'studentId'],
    //             properties: {
    //                 classesId: { type: 'string' },
    //                 studentId: { type: 'string' },
    //             },
    //         },
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { classesId, studentId } = request.params;
    //         const client = await fastify.pg.connect();
    //         try {
    //             const result = await client.query(
    //                 `DELETE FROM student_classes WHERE "classesId" = $1 AND "studentId" = $2 RETURNING *`,
    //                 [classesId, studentId]
    //             );

    //             const currentDate = new Date().getTime()

    //             const studentWeeklyScheduleResult = await fastify.prisma.studentWeeklySchedule.findMany({
    //                 where: {
    //                     studentId,
    //                     institutionId: user.institutionId,
    //                     classesSchedule: {
    //                         startDate: {
    //                             gt: currentDate
    //                         }
    //                     }

    //                 }
    //             })
    //             studentWeeklyScheduleResult.map(async (item) => {
    //                 await fastify.prisma.studentWeeklySchedule.delete({
    //                     where: {
    //                         id: item.id
    //                     }
    //                 })
    //             })

    //             if (result.rows.length === 0) {
    //                 throw new AUTH_ERROR('班级不存在');
    //             }
    //             reply.success({
    //                 data: result.rows[0],
    //                 message: '班级删除学员成功'
    //             });
    //         } catch (error) {
    //             fastify.log.error(error);
    //             throw new INTERNAL_ERROR(error.message || '班级删除学员失败');
    //         } finally {
    //             client.release();
    //         }
    //     }
    // })

    // // 获取学员班级
    // fastify.get('/students/:studentId/classes', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员班级',
    //         params: {
    //             type: 'object',
    //             required: ['studentId'],
    //             properties: {
    //                 studentId: { type: 'string' }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const user = request.user;
    //         const result = await fastify.prisma.studentClasses.findMany({
    //             where: {
    //                 studentId,
    //                 institutionId: user.institutionId
    //             },
    //             select: {
    //                 id: true,
    //                 joinDate: true,
    //                 operatorTime: true,
    //                 type: true,
    //                 classes: {
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                         course: {
    //                             select: {
    //                                 id: true,
    //                                 name: true,

    //                             }
    //                         },
    //                         teacher: {
    //                             select: {
    //                                 id: true,
    //                                 name: true,
    //                             }
    //                         }
    //                     }
    //                 },
    //                 student: {
    //                     select: {
    //                         id: true,
    //                         name: true,
    //                     }
    //                 }
    //             }
    //         })
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             joinDate: Number(item.joinDate),
    //             operatorTime: Number(item.operatorTime),
    //             startTime: Number(item.startTime),
    //             endTime: Number(item.endTime),
    //         }))
    //         reply.success({
    //             data: serializedResult,
    //             message: '获取学员班级成功'
    //         });
    //     }
    // })

    // // 获取学员考勤记录
    // fastify.get('/students/:studentId/attendance', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员考勤记录',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number', default: 1 },
    //                 pageSize: { type: 'number', default: 10 },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const studentId = request.params.studentId;
    //         const user = request.user;
    //         const { page = 1, pageSize = 10 } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;
    //         const [result, total] = await Promise.all([
    //             fastify.prisma.studentWeeklySchedule.findMany({
    //                 where: {
    //                     studentId,
    //                     institutionId: user.institutionId,
    //                     status: 'attendance'
    //                 },
    //                 select: {
    //                     id: true,
    //                     status: true,
    //                     attendanceCount: true,
    //                     operator: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                     operatorTime: true,
    //                     studentProduct: {
    //                         select: {
    //                             id: true,
    //                             product: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             }
    //                         }
    //                     },
    //                     classesSchedule: {
    //                         select: {
    //                             id: true,
    //                             startDate: true,
    //                             startTime: true,
    //                             endTime: true,
    //                             subject: true,
    //                             courses: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             },
    //                             classes: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             }
    //                         }
    //                     },
    //                     student: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                 },
    //                 skip,
    //                 take
    //             }),
    //             fastify.prisma.studentWeeklySchedule.count({
    //                 where: {
    //                     studentId,
    //                     institutionId: user.institutionId,
    //                     status: 'attendance'
    //                 }
    //             })
    //         ])
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             operatorTime: Number(item.operatorTime),
    //             classesSchedule: {
    //                 ...item.classesSchedule,
    //                 startDate: Number(item.classesSchedule.startDate),
    //             }
    //         }))

    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total: total,
    //                 page: page,
    //                 pageSize: pageSize
    //             },
    //             message: '获取学员考勤记录成功'
    //         });
    //     }
    // })


    // // 获取考勤记录
    // fastify.get('/students/attendanceRecords', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员考勤记录',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 search: { type: 'string' },
    //                 page: { type: 'number', default: 1 },
    //                 pageSize: { type: 'number', default: 10 },
    //                 status: {
    //                     type: 'string',
    //                     default: 'attendance',
    //                     enum: ['attendance', 'leave', 'absent', 'unattended']
    //                 },
    //                 endTime: { type: 'number' },
    //                 startTime: { type: 'number' },
    //                 teacherId: { type: 'string' }
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { page = 1, pageSize = 10, status, teacherId, endTime, startTime, search } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;


    //         const where = {
    //             institutionId: user.institutionId,
    //             ...(status && { status }),
    //             ...(teacherId && { operatorId: teacherId }),
    //             ...(search && {
    //                 student: { name: { contains: search, mode: 'insensitive' } }
    //             })
    //         }
    //         if (endTime || startTime) {
    //             where.operatorTime = {
    //                 ...(endTime ? { lte: Number(endTime) } : {}),
    //                 ...(startTime ? { gte: Number(startTime) } : {}),

    //             }
    //         }
    //         console.log(where)
    //         const [result, total] = await Promise.all([
    //             fastify.prisma.studentWeeklySchedule.findMany({
    //                 where,
    //                 select: {
    //                     id: true,
    //                     status: true,
    //                     attendanceCount: true,
    //                     attendanceAmount: true,
    //                     operator: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                     operatorTime: true,
    //                     student: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                     product: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                     classesSchedule: {
    //                         select: {
    //                             id: true,
    //                             startDate: true,
    //                             startTime: true,
    //                             endTime: true,
    //                             subject: true,
    //                             courses: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             },
    //                             classes: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             },
    //                             teacher: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             }

    //                         }
    //                     }
    //                 },
    //                 skip,
    //                 take,
    //             }),
    //             fastify.prisma.studentWeeklySchedule.count({
    //                 where,
    //             })
    //         ])
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             operatorTime: Number(item.operatorTime),
    //             classesSchedule: {
    //                 ...item.classesSchedule,
    //                 startDate: Number(item.classesSchedule.startDate),
    //             }
    //         }))
    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total: total,
    //                 page: page,
    //                 pageSize: pageSize
    //             },
    //             message: '获取学员考勤记录成功.'
    //         })
    //     }
    // })
    // // 获取跟进记录
    // fastify.get('/students/followRecords', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取跟进记录',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number', default: 1 },
    //                 pageSize: { type: 'number', default: 10 },
    //                 teacherId: { type: 'string' },
    //                 search: { type: 'string' },
    //                 endTime: { type: 'number' },
    //                 startTime: { type: 'number' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { page = 1, pageSize = 10, teacherId, search,
    //             endTime, startTime } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;
    //         const where = {
    //             institutionId: user.institutionId,
    //             ...(teacherId && { followUpUserId: teacherId }),
    //             ...(search && {
    //                 student: {
    //                     name: { contains: search, mode: 'insensitive' },
    //                     phone: { contains: search, mode: 'insensitive' }
    //                 }
    //             })
    //         }
    //         if (endTime || startTime) {
    //             where.followUpDate = {
    //                 lte: Number(endTime),
    //                 gte: Number(startTime),
    //             }
    //         }
    //         const [result, total] = await Promise.all([
    //             fastify.prisma.studentFollowRecords.findMany({
    //                 where,
    //                 select: {
    //                     id: true,
    //                     followUpDate: true,
    //                     followUpContent: true,
    //                     nextFollowUpDate: true,
    //                     followUpUser: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                     student: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                             phone: true,
    //                             gender: true,
    //                             birthday: true,
    //                         }
    //                     }
    //                 },
    //                 orderBy: {
    //                     followUpDate: 'desc',
    //                 },
    //                 skip,
    //                 take
    //             }),
    //             fastify.prisma.studentFollowRecords.count({
    //                 where
    //             })
    //         ])

    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             followUpDate: Number(item.followUpDate),
    //             nextFollowUpDate: Number(item.nextFollowUpDate),
    //             student: {
    //                 ...item.student,
    //                 birthday: Number(item.student.birthday),
    //             }
    //         }))
    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total: total,
    //                 page: page,
    //                 pageSize: pageSize
    //             },
    //             message: '获取跟进记录成功.'
    //         })
    //     }
    // })

    // // 获取学员产品调整记录
    // fastify.get('/students/productAdjustments', {
    //     schema: {
    //         tags: ['students'],
    //         summary: '获取学员产品调整记录',
    //         querystring: {
    //             type: 'object',
    //             properties: {
    //                 page: { type: 'number', default: 1 },
    //                 pageSize: { type: 'number', default: 10 },
    //                 search: { type: 'string' },
    //                 teacherId: { type: 'string' },
    //                 endTime: { type: 'number' },
    //                 startTime: { type: 'number' },
    //             }
    //         }
    //     },
    //     onRequest: [fastify.auth.authenticate],
    //     handler: async (request, reply) => {
    //         const user = request.user;
    //         const { page = 1, pageSize = 10, search, endTime,
    //             startTime, teacherId } = request.query;
    //         const skip = (page - 1) * pageSize;
    //         const take = pageSize;
    //         const where = {
    //             institutionId: user.institutionId,
    //             ...(search && {
    //                 student: {
    //                     name: { contains: search, mode: 'insensitive' },
    //                     phone: { contains: search, mode: 'insensitive' }
    //                 }
    //             }),
    //             ...(teacherId && { operatorId: teacherId })
    //         }
    //         if (endTime || startTime) {
    //             where.operatorTime = {
    //                 ...(endTime ? { lte: Number(endTime) } : {}),
    //                 ...(startTime ? { gte: Number(startTime) } : {}),
    //             }
    //         }
    //         const [result, total] = await Promise.all([
    //             fastify.prisma.studentProductAdjust.findMany({
    //                 where,
    //                 select: {
    //                     id: true,
    //                     operatorTime: true,
    //                     type: true,
    //                     // amount: true,
    //                     beforeCount: true,
    //                     afterCount: true,
    //                     beforeDays: true,
    //                     afterDays: true,
    //                     days: true,
    //                     count: true,
    //                     remarks: true,
    //                     operatorTime: true,
    //                     operator: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     },
    //                     studentProduct: {
    //                         select: {
    //                             product: {
    //                                 select: {
    //                                     id: true,
    //                                     name: true,
    //                                 }
    //                             }
    //                         }
    //                     },
    //                     student: {
    //                         select: {
    //                             id: true,
    //                             name: true,
    //                         }
    //                     }
    //                     // operator: {
    //                     //     select: {
    //                     //         id: true,
    //                     //         name: true,
    //                     //     }
    //                     // },
    //                     // student: {
    //                     //     select: {
    //                     //         id: true,
    //                     //         name: true,
    //                     //         phone: true,
    //                     //     }
    //                     // },
    //                     // studentProduct: {
    //                     //     product: {
    //                     //         select: {
    //                     //             id: true,
    //                     //             name: true,
    //                     //         }
    //                     //     }
    //                     // }
    //                 },
    //                 skip,
    //                 take,
    //                 orderBy: {
    //                     operatorTime: 'desc',
    //                 }
    //             }),
    //             fastify.prisma.studentProductAdjust.count({
    //                 where
    //             })
    //         ])
    //         const serializedResult = result.map(item => ({
    //             ...item,
    //             operatorTime: Number(item.operatorTime),
    //         }))
    //         reply.success({
    //             data: {
    //                 list: serializedResult,
    //                 total: total,
    //                 page: page,
    //                 pageSize: pageSize
    //             },
    //             message: '获取学员产品调整记录成功.'
    //         })
    //     }
    // })
}