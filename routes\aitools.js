import { v4 as uuidv4 } from 'uuid';
import {
    addImageGenerationJob,
    addImageVariationJob,
    addTextGenerationJob,
    addTextToSpeechJob,
    addSpeechToTextJob,
    addImageAnalysisJob,
    addImageCaptioningJob
} from '../queues/index.js';

export default async function (fastify, opts) {
    // 图片生成
    fastify.post('/aitools/generate-image', {
        schema: {
            tags: ['aitools'],
            summary: '图片生成',
            description: '根据文本提示生成图片',
            body: {
                type: 'object',
                required: ['prompt'],
                properties: {
                    prompt: { type: 'string', description: '图片生成提示词' },
                    count: { type: 'number', default: 1, description: '生成图片数量' },
                    size: { type: 'string', default: '1024x1024', description: '图片尺寸，如 1024x1024, 512x512' },
                    style: { type: 'string', enum: ['realistic', 'cartoon', 'artistic', 'sketch'], description: '图片风格' }
                }
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        message: { type: 'string' },
                        data: {
                            type: 'object',
                            properties: {
                                taskId: { type: 'string' }
                            }
                        }
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { prompt, count, size, style } = request.body

            // 处理机构的ai次数
            // TODO: 实现机构AI次数限制逻辑

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, prompt, type: 'image-generation' }),
                'EX',
                3600
            )
            await addImageGenerationJob(taskId, prompt, count, size, style)

            return reply.success({
                message: '图片生成任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 图片变体生成
    fastify.post('/aitools/image-variation', {
        schema: {
            tags: ['aitools'],
            summary: '图片变体生成',
            description: '基于现有图片生成变体',
            body: {
                type: 'object',
                required: ['imageUrl'],
                properties: {
                    imageUrl: { type: 'string', description: '原始图片URL' },
                    count: { type: 'number', default: 1, description: '生成变体数量' },
                    variationStrength: { type: 'number', minimum: 0, maximum: 1, default: 0.5, description: '变化强度，0-1之间' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { imageUrl, count, variationStrength } = request.body

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, imageUrl, type: 'image-variation' }),
                'EX',
                3600
            )

            await addImageVariationJob(taskId, imageUrl, count, variationStrength)

            return reply.success({
                message: '图片变体生成任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 文本生成
    fastify.post('/aitools/generate-text', {
        schema: {
            tags: ['aitools'],
            summary: '文本生成',
            description: '使用AI生成文本内容',
            body: {
                type: 'object',
                required: ['prompt'],
                properties: {
                    prompt: { type: 'string', description: '文本生成提示词' },
                    maxTokens: { type: 'number', default: 1000, description: '生成文本的最大长度' },
                    temperature: { type: 'number', minimum: 0, maximum: 1, default: 0.7, description: '生成文本的随机性，0-1之间' },
                    model: { type: 'string', enum: ['qianfan-chinese', 'qianfan-english', 'qianfan-code'], default: 'qianfan-chinese', description: '使用的模型' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { prompt, maxTokens, temperature, model } = request.body

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, prompt, type: 'text-generation' }),
                'EX',
                3600
            )

            await addTextGenerationJob(taskId, prompt, maxTokens, temperature, model)

            return reply.success({
                message: '文本生成任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 文本转语音
    fastify.post('/aitools/text-to-speech', {
        schema: {
            tags: ['aitools'],
            summary: '文本转语音',
            description: '将文本转换为语音',
            body: {
                type: 'object',
                required: ['text'],
                properties: {
                    text: { type: 'string', description: '要转换的文本' },
                    voice: { type: 'string', enum: ['female', 'male', 'child'], default: 'female', description: '语音类型' },
                    speed: { type: 'number', minimum: 0.5, maximum: 2, default: 1, description: '语速，0.5-2之间' },
                    format: { type: 'string', enum: ['mp3', 'wav'], default: 'mp3', description: '音频格式' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { text, voice, speed, format } = request.body

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, text, type: 'text-to-speech' }),
                'EX',
                3600
            )

            await addTextToSpeechJob(taskId, text, voice, speed, format)

            return reply.success({
                message: '文本转语音任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 语音转文本
    fastify.post('/aitools/speech-to-text', {
        schema: {
            tags: ['aitools'],
            summary: '语音转文本',
            description: '将语音转换为文本',
            body: {
                type: 'object',
                required: ['audioUrl'],
                properties: {
                    audioUrl: { type: 'string', description: '语音文件URL' },
                    language: { type: 'string', enum: ['zh', 'en', 'auto'], default: 'zh', description: '语言' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { audioUrl, language } = request.body

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, audioUrl, type: 'speech-to-text' }),
                'EX',
                3600
            )

            await addSpeechToTextJob(taskId, audioUrl, language)

            return reply.success({
                message: '语音转文本任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 图像分析
    fastify.post('/aitools/image-analysis', {
        schema: {
            tags: ['aitools'],
            summary: '图像分析',
            description: '分析图像内容',
            body: {
                type: 'object',
                required: ['imageUrl'],
                properties: {
                    imageUrl: { type: 'string', description: '图像URL' },
                    analysisType: {
                        type: 'string',
                        enum: ['general', 'objects', 'faces', 'text', 'nsfw'],
                        default: 'general',
                        description: '分析类型'
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { imageUrl, analysisType } = request.body

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, imageUrl, type: 'image-analysis' }),
                'EX',
                3600
            )

            await addImageAnalysisJob(taskId, imageUrl, analysisType)

            return reply.success({
                message: '图像分析任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 图像描述
    fastify.post('/aitools/image-caption', {
        schema: {
            tags: ['aitools'],
            summary: '图像描述',
            description: '生成图像的文字描述',
            body: {
                type: 'object',
                required: ['imageUrl'],
                properties: {
                    imageUrl: { type: 'string', description: '图像URL' },
                    language: { type: 'string', enum: ['zh', 'en'], default: 'zh', description: '描述语言' },
                    detailLevel: { type: 'string', enum: ['basic', 'detailed'], default: 'detailed', description: '描述详细程度' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { imageUrl, language, detailLevel } = request.body

            const taskId = uuidv4()

            await fastify.redisTask.set(
                taskId,
                JSON.stringify({ status: 'pending', taskId, imageUrl, type: 'image-caption' }),
                'EX',
                3600
            )

            await addImageCaptioningJob(taskId, imageUrl, language, detailLevel)

            return reply.success({
                message: '图像描述任务已提交',
                data: {
                    taskId
                }
            })
        }
    })

    // 获取任务状态
    fastify.get('/aitools/task/:taskId', {
        schema: {
            tags: ['aitools'],
            summary: '获取任务状态',
            description: '获取AI任务的状态和结果',
            params: {
                type: 'object',
                required: ['taskId'],
                properties: {
                    taskId: { type: 'string', description: '任务ID' }
                }
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        message: { type: 'string' },
                        data: {
                            type: 'object',
                            properties: {
                                taskId: { type: 'string' },
                                status: { type: 'string', enum: ['pending', 'processing', 'completed', 'failed'] },
                                progress: { type: 'number' },
                                result: { type: 'object' },
                                error: { type: 'string' },
                                processingTime: { type: 'number' }
                            }
                        }
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { taskId } = request.params

            const taskData = await fastify.redisTask.get(`task:${taskId}`)

            if (!taskData) {
                return reply.code(404).send({
                    code: 404,
                    message: '任务不存在或已过期',
                    data: null
                })
            }

            return reply.success({
                message: '获取任务状态成功',
                data: JSON.parse(taskData)
            })
        }
    })
}
