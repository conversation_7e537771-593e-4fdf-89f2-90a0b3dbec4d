import { createError } from '@fastify/error';

import { userRoleMenus } from '../schemas/user.js';

import menuService from '../services/menuService-new.js';

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

export default async function (fastify, opts) {
    // 获取用户角色菜单
    fastify.get('/user/role-menus', {
        schema: userRoleMenus,
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            try {
                const user = request.user;
                
                // 获取用户角色菜单
                const menuTree = await menuService.getUserMenus({
                    user,
                    fastify: request.server
                });
                
                return reply.success({
                    data: menuTree,
                    message: '获取用户角色菜单成功'
                });
            } catch (error) {
                request.log.error({
                    msg: '获取用户角色菜单失败',
                    error: error.message,
                    stack: error.stack,
                    userId: request.user?.id
                });
                
                if (error instanceof AUTH_ERROR) {
                    throw error;
                }
                
                throw new INTERNAL_ERROR('获取用户角色菜单失败');
            }
        }
    });

    // 获取当前用户角色权限
    fastify.get('/user/current-permissions', {
        schema: {
            tags: ['user'],
            summary: '获取当前用户角色权限',
            description: '获取当前登录用户的所有角色权限代码',
            response: {
                200: {
                    type: 'object',
                    properties: {
                        code: { type: 'number' },
                        message: { type: 'string' },
                        data: {
                            type: 'object',
                            properties: {
                                permissionCodes: {
                                    type: 'array',
                                    items: { type: 'string' }
                                }
                            }
                        }
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            try {
                const user = request.user;
                const client = await request.server.pg.connect();
                
                try {
                    // 使用 PostgreSQL 原生查询获取用户权限代码
                    const result = await client.query(
                        `SELECT DISTINCT p.code
                         FROM permissions p
                         JOIN role_permissions rp ON p.id = rp."permissionId"
                         JOIN roles r ON rp."roleId" = r.id
                         JOIN user_roles ur ON r.id = ur."roleId"
                         WHERE ur."userId" = $1
                         ORDER BY p.code`,
                        [user.id]
                    );

                    const permissionCodes = result.rows.map(row => row.code);

                    return reply.success({
                        data: {
                            permissionCodes
                        },
                        message: '获取当前用户角色权限成功'
                    });
                } finally {
                    client.release();
                }
            } catch (error) {
                request.log.error({
                    msg: '获取当前用户角色权限失败',
                    error: error.message,
                    stack: error.stack,
                    userId: request.user?.id
                });
                
                if (error instanceof AUTH_ERROR) {
                    throw error;
                }
                
                throw new INTERNAL_ERROR('获取当前用户角色权限失败');
            }
        }
    });
}