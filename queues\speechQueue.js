/**
 * Speech processing queue for BullMQ
 * Handles text-to-speech and speech-to-text tasks
 */

import { Queue } from "bullmq";
import { redisConfig } from "../config/redis.js";
import { defaultQueueOptions } from "./queueConfig.js";

// Queue configuration with specific settings for speech processing
const queueOptions = {
  ...defaultQueueOptions,
  limiter: {
    max: 5,
    duration: 1000,
    groupKey: 'speech-processing'
  }
};

// Create the queue
const speechQueue = new Queue('speech-processing', queueOptions);

// Add error handling for the queue
speechQueue.on('error', (error) => {
  console.error('Speech processing queue error:', error);
});

speechQueue.on('failed', (job, error) => {
  console.error(`Speech processing job ${job.id} failed:`, error);
});

/**
 * Add a text-to-speech job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} text - The text to convert to speech
 * @param {string} voice - The voice to use
 * @param {number} speed - The speed of the speech
 * @param {string} format - The format of the output audio
 * @returns {Promise<Job>} - The created job
 */
export async function addTextToSpeechJob(taskId, text, voice, speed, format) {
  try {
    return await speechQueue.add('text-to-speech', {
      taskId,
      type: 'text-to-speech',
      text,
      voice,
      speed,
      format
    });
  } catch (error) {
    console.error('Error adding text-to-speech job:', error);
    throw error;
  }
}

/**
 * Add a speech-to-text job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} audioUrl - The URL of the audio file
 * @param {string} language - The language of the audio
 * @returns {Promise<Job>} - The created job
 */
export async function addSpeechToTextJob(taskId, audioUrl, language) {
  try {
    return await speechQueue.add('speech-to-text', {
      taskId,
      type: 'speech-to-text',
      audioUrl,
      language
    });
  } catch (error) {
    console.error('Error adding speech-to-text job:', error);
    throw error;
  }
}

export default speechQueue;
