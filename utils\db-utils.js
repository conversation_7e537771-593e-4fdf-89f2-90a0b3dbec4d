

/**
 * 执行带有超时和性能监控的数据库查询
 * @param {Object} client - 数据库客户端
 * @param {Object} fastify - Fastify实例
 * @param {string} query - SQL查询语句
 * @param {Array} params - 查询参数
 * @param {Object} options - 选项
 * @param {number} options.timeout - 查询超时时间（毫秒）
 * @param {boolean} options.useTransaction - 是否使用事务
 * @param {string} options.queryName - 查询名称（用于日志）
 * @returns {Promise<Object>} 查询结果
 */
export async function executeQuery(client, fastify, query, params = [], options = {}) {
    // 合并选项
    const opts = {
        timeout: 10000, // 默认查询超时时间为10秒
        useTransaction: false,
        queryName: 'unnamed-query',
        ...options
    };

    // 记录开始时间
    const startTime = process.hrtime.bigint();

    // 创建查询信息对象（用于日志和错误报告）
    const queryInfo = {
        query,
        params,
        queryName: opts.queryName,
        startTime: new Date().toISOString()
    };

    // 创建超时Promise
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            const duration = Number(process.hrtime.bigint() - startTime) / 1000000;
            reject(new Error(`查询超时: ${opts.queryName} (${duration.toFixed(2)}ms)`));
        }, opts.timeout);
    });

    try {
        // 如果需要使用事务，则开始事务
        if (opts.useTransaction) {
            await client.query('BEGIN');
        }

        // 执行查询，与超时Promise竞争
        const result = await Promise.race([
            client.query(query, params),
            timeoutPromise
        ]);

        // 如果使用事务，则提交事务
        if (opts.useTransaction) {
            await client.query('COMMIT');
        }

        // 计算查询时间
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒

        // 记录慢查询
        if (duration > 1000) { // 超过1秒的查询视为慢查询
            console.warn(`慢查询: ${opts.queryName} (${duration.toFixed(2)}ms)`, {
                query,
                params,
                rowCount: result.rowCount
            });
        }

        return result;
    } catch (error) {
        // 如果使用事务，则回滚事务
        if (opts.useTransaction) {
            await client.query('ROLLBACK').catch(e => {
                console.error('事务回滚失败:', e);
            });
        }

        // 计算查询时间
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒

        // 记录查询错误
        console.error(`查询错误: ${opts.queryName} (${duration.toFixed(2)}ms)`, {
            query,
            params,
            error: error.message
        });

        // 重新抛出错误
        throw error;
    }
}

/**
 * 执行带有缓存的数据库查询
 * @param {Object} client - 数据库客户端
 * @param {Object} fastify - Fastify实例
 * @param {string} query - SQL查询语句
 * @param {Array} params - 查询参数
 * @param {Object} options - 选项
 * @param {string} options.cacheKey - 缓存键
 * @param {number} options.cacheTTL - 缓存过期时间（秒）
 * @param {boolean} options.useTransaction - 是否使用事务
 * @param {string} options.queryName - 查询名称（用于日志）
 * @returns {Promise<Object>} 查询结果
 */
export async function executeQueryWithCache(client, fastify, query, params = [], options = {}) {
    // 合并选项
    const opts = {
        cacheKey: null,
        cacheTTL: 300, // 默认缓存5分钟
        useTransaction: false,
        queryName: 'unnamed-cached-query',
        ...options
    };

    // 如果没有提供缓存键，则生成一个
    if (!opts.cacheKey) {
        const queryHash = require('crypto')
            .createHash('md5')
            .update(query + JSON.stringify(params))
            .digest('hex');
        opts.cacheKey = `db:query:${queryHash}`;
    }

    // 尝试从缓存获取结果
    if (fastify?.redis) {
        try {
            const cachedResult = await fastify.redis.get(opts.cacheKey);
            if (cachedResult) {
                return JSON.parse(cachedResult);
            }
        } catch (error) {
            console.warn('从缓存获取查询结果失败:', error);
            // 继续执行查询
        }
    }

    // 执行查询
    const result = await executeQuery(client, fastify, query, params, opts);

    // 将结果存入缓存
    if (fastify?.redis) {
        try {
            await fastify.redis.set(opts.cacheKey, JSON.stringify(result), 'EX', opts.cacheTTL);
        } catch (error) {
            console.warn('将查询结果存入缓存失败:', error);
            // 继续返回结果
        }
    }

    return result;
}

/**
 * 批量执行查询
 * @param {Object} client - 数据库客户端
 * @param {Object} fastify - Fastify实例
 * @param {Array} queries - 查询数组，每个元素包含 {query, params, options}
 * @param {boolean} useTransaction - 是否使用事务包装所有查询
 * @returns {Promise<Array>} 查询结果数组
 */
export async function executeBatchQueries(client, fastify, queries, useTransaction = true) {
    // 记录开始时间
    const startTime = process.hrtime.bigint();

    try {
        // 如果需要使用事务，则开始事务
        if (useTransaction) {
            await client.query('BEGIN');
        }

        // 执行所有查询
        const results = [];
        for (const { query, params, options } of queries) {
            const result = await executeQuery(client, fastify, query, params, {
                ...options,
                useTransaction: false // 批处理中的单个查询不需要自己的事务
            });
            results.push(result);
        }

        // 如果使用事务，则提交事务
        if (useTransaction) {
            await client.query('COMMIT');
        }

        // 计算总查询时间
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒

        // 记录批处理性能
        if (duration > 1000) { // 超过1秒的批处理视为慢查询
            console.warn(`慢批处理查询 (${duration.toFixed(2)}ms)`, {
                queryCount: queries.length
            });
        }

        return results;
    } catch (error) {
        // 如果使用事务，则回滚事务
        if (useTransaction) {
            await client.query('ROLLBACK').catch(e => {
                console.error('批处理事务回滚失败:', e);
            });
        }

        // 重新抛出错误
        throw error;
    }
}
