/**
 * Speech processing worker for BullMQ
 * Handles text-to-speech and speech-to-text tasks
 */

import { Worker } from "bullmq";
import { redisTask } from "../config/redis.js";
import { textToSpeech, speechToText } from "../services/speechService.js";
import {
  defaultWorkerOptions,
  markTaskCompleted,
  markTaskFailed,
  setupProgressReporting
} from "./workerConfig.js";
import speechQueue from "../queues/speechQueue.js";

// Create the worker
const worker = new Worker(
  'speech-processing',
  async (job) => {
    const { taskId, type } = job.data;
    console.log(`Starting speech processing job ${job.id}, taskId: ${taskId}, type: ${type}`);

    const startTime = Date.now();

    // Setup progress reporting
    const { progressInterval, updateProgress } = setupProgressReporting(taskId);

    try {
      let result;

      // Process different types of speech processing tasks
      if (type === 'text-to-speech') {
        const { text, voice, speed } = job.data;

        // Update initial progress
        await updateProgress(10);

        // Convert text to speech
        result = await textToSpeech(text, voice, speed);
      } else if (type === 'speech-to-text') {
        const { audioUrl, language } = job.data;

        // Update initial progress
        await updateProgress(10);

        // Convert speech to text
        result = await speechToText(audioUrl, language);
      } else {
        throw new Error(`Unsupported speech task type: ${type}`);
      }

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Mark task as completed
      await markTaskCompleted(taskId, result, type, processingTime);

      console.log(`Speech processing job ${job.id} completed, processing time: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`Speech processing job ${job.id} failed:`, error);

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Mark task as failed
      await markTaskFailed(taskId, error, type, Date.now() - startTime);

      // Rethrow the error for BullMQ to handle retries
      throw error;
    }
  },
  defaultWorkerOptions
);

// Add error handling for the worker
worker.on('error', (error) => {
  console.error('Speech processing worker error:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Speech processing worker job ${job?.id} failed:`, error);
});

worker.on('completed', (job) => {
  console.log(`Speech processing worker completed job ${job.id}`);
});

export default speechQueue;
