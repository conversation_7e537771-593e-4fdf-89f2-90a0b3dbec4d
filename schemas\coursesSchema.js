/**
 * Courses Schema
 * Defines validation schemas for course-related endpoints
 */
const coursesSchema = {
    /**
     * Schema for getting courses list
     */
    getCoursesListSchema: {
        tags: ['courses'],
        summary: '获取课程列表',
        description: '获取课程列表，支持分页和搜索',
        querystring: {
            type: 'object',
            properties: {
                page: { 
                    type: 'number', 
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: { 
                    type: 'number', 
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: { 
                    type: 'string',
                    description: '搜索关键词，支持课程名称搜索'
                },
                type: { 
                    type: 'string',
                    description: '课程类型，如group（小组课）、one-on-one（一对一）等'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        type: { type: 'string' },
                                        duration: { type: 'number' },
                                        teacherId: { type: 'string' },
                                        isDirectSale: { type: 'boolean' },
                                        deductionPerClass: { type: 'number' },
                                        status: { type: 'string' },
                                        picture: { type: 'string' },
                                        isShow: { type: 'boolean' },
                                        description: { type: 'string' },
                                        price: { type: 'number' },
                                        isDeductOnAttendance: { type: 'boolean' },
                                        isDeductOnLeave: { type: 'boolean' },
                                        isDeductOnAbsence: { type: 'boolean' },
                                        products: {
                                            type: 'array',
                                            items: {
                                                type: 'object',
                                                properties: {
                                                    id: { type: 'string' },
                                                    name: { type: 'string' }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting courses select list
     */
    getCoursesSelectListSchema: {
        tags: ['courses'],
        summary: '获取课程选择列表',
        description: '获取课程选择列表，用于下拉选择框',
        querystring: {
            type: 'object',
            properties: {
                id: { 
                    type: 'boolean',
                    default: true,
                    description: '是否包含ID'
                },
                name: { 
                    type: 'boolean',
                    default: true,
                    description: '是否包含名称'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                name: { type: 'string' }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for creating course
     */
    createCourseSchema: {
        tags: ['courses'],
        summary: '创建课程',
        description: '创建新课程',
        body: {
            type: 'object',
            required: ['name'],
            properties: {
                name: { 
                    type: 'string',
                    description: '课程名称'
                },
                type: { 
                    type: 'string',
                    default: 'group',
                    description: '课程类型，如group（小组课）、one-on-one（一对一）等'
                },
                picture: { 
                    type: 'string',
                    default: '',
                    description: '课程封面图片URL'
                },
                isShow: { 
                    type: 'boolean',
                    default: true,
                    description: '是否显示'
                },
                duration: { 
                    type: 'number',
                    default: 60,
                    description: '课程时长（分钟）'
                },
                description: { 
                    type: 'string',
                    description: '课程描述'
                },
                isDirectSale: { 
                    type: 'boolean',
                    default: false,
                    description: '是否直接销售'
                },
                price: { 
                    type: 'number',
                    default: 0,
                    description: '直销价格'
                },
                deductionPerClass: { 
                    type: 'number',
                    default: 1,
                    description: '每节课扣除课时数'
                },
                isDeductOnAttendance: { 
                    type: 'boolean',
                    default: true,
                    description: '出勤是否扣课时'
                },
                isDeductOnLeave: { 
                    type: 'boolean',
                    default: false,
                    description: '请假是否扣课时'
                },
                isDeductOnAbsence: { 
                    type: 'boolean',
                    default: false,
                    description: '缺勤是否扣课时'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            type: { type: 'string' },
                            duration: { type: 'number' },
                            isDirectSale: { type: 'boolean' },
                            price: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for updating course
     */
    updateCourseSchema: {
        tags: ['courses'],
        summary: '更新课程',
        description: '更新课程信息',
        params: {
            type: 'object',
            required: ['courseId'],
            properties: {
                courseId: { 
                    type: 'string',
                    description: '课程ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                name: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                picture: { type: 'string' },
                isShow: { type: 'boolean' },
                duration: { type: 'number' },
                description: { type: 'string' },
                isDirectSale: { type: 'boolean' },
                price: { type: 'number' },
                deductionPerClass: { type: 'number' },
                isDeductOnAttendance: { type: 'boolean' },
                isDeductOnLeave: { type: 'boolean' },
                isDeductOnAbsence: { type: 'boolean' }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            type: { type: 'string' },
                            status: { type: 'string' },
                            duration: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for deleting course
     */
    deleteCourseSchema: {
        tags: ['courses'],
        summary: '删除课程',
        description: '删除课程',
        params: {
            type: 'object',
            required: ['courseId'],
            properties: {
                courseId: { 
                    type: 'string',
                    description: '课程ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting course by ID
     */
    getCourseByIdSchema: {
        tags: ['courses'],
        summary: '获取课程详情',
        description: '根据ID获取课程详情',
        params: {
            type: 'object',
            required: ['courseId'],
            properties: {
                courseId: { 
                    type: 'string',
                    description: '课程ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            type: { type: 'string' },
                            duration: { type: 'number' },
                            teacherId: { type: 'string' },
                            isDirectSale: { type: 'boolean' },
                            deductionPerClass: { type: 'number' },
                            status: { type: 'string' },
                            picture: { type: 'string' },
                            isShow: { type: 'boolean' },
                            description: { type: 'string' },
                            price: { type: 'number' },
                            isDeductOnAttendance: { type: 'boolean' },
                            isDeductOnLeave: { type: 'boolean' },
                            isDeductOnAbsence: { type: 'boolean' },
                            products: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        price: { type: 'number' }
                                    }
                                }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for associating course with product
     */
    associateCourseWithProductSchema: {
        tags: ['courses'],
        summary: '关联课程与产品',
        description: '将课程关联到产品',
        params: {
            type: 'object',
            required: ['courseId'],
            properties: {
                courseId: { 
                    type: 'string',
                    description: '课程ID'
                }
            }
        },
        body: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for dissociating course from product
     */
    dissociateCourseFromProductSchema: {
        tags: ['courses'],
        summary: '解除课程与产品关联',
        description: '解除课程与产品的关联',
        params: {
            type: 'object',
            required: ['courseId', 'productId'],
            properties: {
                courseId: { 
                    type: 'string',
                    description: '课程ID'
                },
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    }
};

export default coursesSchema;
