import { v4 as uuidv4 } from 'uuid';
import { sendToAllUsersInInstitution } from '../libs/websocket.js';

/**
 * 通知服务
 */
export class NotificationService {
  /**
   * 创建通知
   * @param {Object} params - 参数对象
   * @param {Object} params.client - Prisma客户端
   * @param {Object} params.user - 当前用户
   * @param {boolean} params.isAll - 是否发送给所有用户
   * @param {Array} params.userIds - 用户ID列表
   * @param {string} params.title - 通知标题
   * @param {string} params.content - 通知内容
   * @param {Object} params.fastify - Fastify实例
   * @returns {Promise<Object>} - 创建结果
   */
  static async createNotification({ client, user, recipientType, recipientIds, title, content, fastify }) {
    let userList = [];
    
    if (recipientType === 'all') {
      userList = await client.userInstitution.findMany({
        where: {
          institutionId: user.institutionId
        }
      });
    } else {
      userList = await client.userInstitution.findMany({
        where: {
          userId: { in: recipientIds },
          institutionId: user.institutionId
        }
      });
    }
    
    // 创建通知
    const notificationId = uuidv4();
    const notification = await client.notification.create({
      data: {
        id: notificationId,
        title,
        content,
        createdById: user.id,
        type: 'institution',
        institutionId: user.institutionId
      }
    });
    
    // 创建用户关联通知
    await client.userNotification.createMany({
      data: userList.map(user => ({
        id: uuidv4(),
        notificationId,
        userId: user.userId,
        status: 'unread'
      }))
    });
    
    // 发送WebSocket通知
    await sendToAllUsersInInstitution(
      fastify,
      user.institutionId,
      {
        type: 'NOTIFICATION_ADD',
        data: {
          id: notificationId,
          title,
        }
      }
    );
    
    return { message: '通知创建成功' };
  }
  
  /**
   * 获取通知列表
   * @param {Object} params - 参数对象
   * @param {Object} params.fastify - Fastify实例
   * @param {Object} params.user - 当前用户
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.status - 通知状态
   * @returns {Promise<Object>} - 通知列表
   */
  static async getNotifications({ fastify, user, page, pageSize, status }) {
    // 构建查询条件
    let whereClause = 'WHERE un."userId" = $1';
    const queryParams = [user.id];
    let paramIndex = 2;
    
    // 添加状态筛选条件
    if (status !== 'all') {
      whereClause += ` AND un.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }
    
    // 计算总数
    const countQuery = `
      SELECT COUNT(*) 
      FROM user_notifications un
      ${whereClause}
    `;
    
    const totalResult = await fastify.pg.query(countQuery, queryParams);
    const total = parseInt(totalResult.rows[0].count);
    
    // 计算分页参数
    const offset = (page - 1) * pageSize;
    
    // 获取通知列表
    const listQuery = `
      SELECT 
        n.id,
        n.title,
        n."createdAt",
        n.type,
        u.name as "creatorName",
        un.status
      FROM 
        user_notifications un
      JOIN 
        notifications n ON un."notificationId" = n.id
      LEFT JOIN 
        users u ON n."createdById" = u.id
      ${whereClause}
      ORDER BY 
        un."createdAt" DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    // 添加分页参数
    queryParams.push(pageSize, offset);
    
    const result = await fastify.pg.query(listQuery, queryParams);
    
    return {
      total,
      list: result.rows,
      page,
      pageSize
    };
  }
  
  /**
   * 获取通知详情
   * @param {Object} params - 参数对象
   * @param {Object} params.fastify - Fastify实例
   * @param {Object} params.user - 当前用户
   * @param {string} params.id - 通知ID
   * @returns {Promise<Object>} - 通知详情
   */
  static async getNotificationDetail({ fastify, user, id }) {
    // 使用PostgreSQL查询通知详情
    const notificationQuery = `
      SELECT 
        n.id,
        n.title,
        n.content,
        n."createdAt",
        n.type,
        u.name as "creatorName"
      FROM 
        notifications n
      LEFT JOIN 
        users u ON n."createdById" = u.id
      WHERE 
        n.id = $1 AND n."institutionId" = $2
    `;
    
    const notificationResult = await fastify.pg.query(notificationQuery, [id, user.institutionId]);
    
    if (notificationResult.rows.length === 0) {
      throw new Error('通知不存在');
    }
    
    const notification = notificationResult.rows[0];
    
    // 查找并更新用户通知状态
    const userNotificationQuery = `
      SELECT id FROM user_notifications 
      WHERE "notificationId" = $1 AND "userId" = $2
    `;
    
    const userNotificationResult = await fastify.pg.query(userNotificationQuery, [id, user.id]);
    
    if (userNotificationResult.rows.length > 0) {
      const userNotificationId = userNotificationResult.rows[0].id;
      
      // 更新通知状态为已读
      const updateQuery = `
        UPDATE user_notifications 
        SET status = 'read', "readAt" = $1 
        WHERE id = $2
      `;
      
      await fastify.pg.query(updateQuery, [Date.now(), userNotificationId]);
    }
    
    return notification;
  }
  
  /**
   * 获取未读通知数量
   * @param {Object} params - 参数对象
   * @param {Object} params.client - Prisma客户端
   * @param {string} params.userId - 用户ID
   * @returns {Promise<number>} - 未读通知数量
   */
  static async getUnreadCount({ client, userId }) {
    const count = await client.userNotification.count({
      where: { userId, status: 'unread' }
    });
    return count;
  }
  
  /**
   * 批量标记通知为已读
   * @param {Object} params - 参数对象
   * @param {Object} params.client - Prisma客户端
   * @param {string} params.userId - 用户ID
   * @param {Array} params.ids - 通知ID列表
   * @returns {Promise<Object>} - 操作结果
   */
  static async markAsRead({ client, userId, ids }) {
    await client.userNotification.updateMany({
      where: {
        notificationId: { in: ids },
        userId,
      },
      data: {
        status: 'read',
        readAt: new Date().getTime()
      }
    });
    
    return { message: '通知已读处理成功' };
  }
  
  /**
   * 标记所有通知为已读
   * @param {Object} params - 参数对象
   * @param {Object} params.client - Prisma客户端
   * @param {string} params.userId - 用户ID
   * @returns {Promise<Object>} - 操作结果
   */
  static async markAllAsRead({ client, userId }) {
    await client.userNotification.updateMany({
      where: {
        userId
      },
      data: {
        status: 'read',
        readAt: new Date().getTime()
      }
    });
    
    return { message: '通知已读处理成功' };
  }
  
  /**
   * 批量删除通知
   * @param {Object} params - 参数对象
   * @param {Object} params.client - Prisma客户端
   * @param {string} params.userId - 用户ID
   * @param {Array} params.ids - 通知ID列表
   * @returns {Promise<Object>} - 操作结果
   */
  static async deleteNotifications({ client, userId, ids }) {
    await client.userNotification.deleteMany({
      where: {    
        notificationId: { in: ids },
        userId
      }
    });
    
    return { message: '通知删除成功' };
  }
} 