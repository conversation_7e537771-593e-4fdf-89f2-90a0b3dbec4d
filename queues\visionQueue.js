/**
 * Vision processing queue for BullMQ
 * Handles image analysis and captioning tasks
 */

import { Queue } from "bullmq";
import { redisConfig } from "../config/redis.js";
import { defaultQueueOptions } from "./queueConfig.js";

// Queue configuration with specific settings for vision processing
const queueOptions = {
  ...defaultQueueOptions,
  limiter: {
    max: 5,
    duration: 1000,
    groupKey: 'vision-processing'
  }
};

// Create the queue
const visionQueue = new Queue('vision-processing', queueOptions);

// Add error handling for the queue
visionQueue.on('error', (error) => {
  console.error('Vision processing queue error:', error);
});

visionQueue.on('failed', (job, error) => {
  console.error(`Vision processing job ${job.id} failed:`, error);
});

/**
 * Add an image analysis job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} imageUrl - The URL of the image to analyze
 * @param {string} analysisType - The type of analysis to perform
 * @returns {Promise<Job>} - The created job
 */
export async function addImageAnalysisJob(taskId, imageUrl, analysisType) {
  try {
    return await visionQueue.add('image-analysis', {
      taskId,
      type: 'image-analysis',
      imageUrl,
      analysisType
    });
  } catch (error) {
    console.error('Error adding image analysis job:', error);
    throw error;
  }
}

/**
 * Add an image captioning job to the queue
 * @param {string} taskId - The ID of the task
 * @param {string} imageUrl - The URL of the image to caption
 * @param {string} language - The language for the caption
 * @param {string} detailLevel - The level of detail for the caption
 * @returns {Promise<Job>} - The created job
 */
export async function addImageCaptioningJob(taskId, imageUrl, language, detailLevel) {
  try {
    return await visionQueue.add('image-caption', {
      taskId,
      type: 'image-caption',
      imageUrl,
      language,
      detailLevel
    });
  } catch (error) {
    console.error('Error adding image captioning job:', error);
    throw error;
  }
}

export default visionQueue;
