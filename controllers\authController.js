import { INTERNAL_ERROR, AUTH_ERROR, NOT_FOUND_ERROR, VALIDATION_ERROR } from '../errors/index.js';
import { authService } from '../services/authService.js';

/**
 * Authentication Controller
 * Handles HTTP requests and responses for authentication-related endpoints
 */
export const authController = {
    /**
     * User login
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async login(request, reply) {
        try {
            const { account, password } = request.body;

            // Call service to login user
            const result = await authService.login({
                account,
                password,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                data: result,
                message: '登录成功'
            });
        } catch (error) {
            request.log.error({
                msg: '登录失败',
                error: error.message,
                stack: error.stack,
                account: request.body?.account
            });

            if (error instanceof AUTH_ERROR || error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('登录失败');
        }
    },

    /**
     * User logout
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async logout(request, reply) {
        try {
            const user = request.user;
            const token = request.headers.authorization?.replace('Bearer ', '');

            if (!user || !user.id) {
                request.log.warn({
                    msg: '登出请求缺少用户信息',
                    headers: request.headers
                });

                // Still return success to client
                return reply.success({
                    message: '登出成功'
                });
            }

            try {
                // Call service to logout user with a timeout
                const logoutPromise = authService.logout({
                    userId: user.id,
                    token,
                    fastify: request.server
                });

                // Create a timeout promise
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error('登出操作超时'));
                    }, 5000); // 5 second timeout
                });

                // Race the promises
                await Promise.race([logoutPromise, timeoutPromise]);
            } catch (serviceError) {
                // Log the error but don't fail the request
                request.log.error({
                    msg: '登出服务操作失败，但仍返回成功给客户端',
                    error: serviceError.message,
                    stack: serviceError.stack,
                    userId: user.id
                });

                // The token is likely already invalidated in Redis by the service
                // So we can still return success to the client
            }

            // Always return success response to client
            return reply.success({
                message: '登出成功'
            });
        } catch (error) {
            request.log.error({
                msg: '登出控制器处理失败',
                error: error.message,
                stack: error.stack,
                userId: request.user?.id
            });

            // Even in case of error, we want to return success to the client
            // This prevents the client from getting stuck in a logged-in state
            return reply.success({
                message: '登出成功'
            });
        }
    },

    /**
     * Get current user info
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async getCurrentUser(request, reply) {
        try {
            const user = request.user;

            // Call service to get current user info
            const userInfo = await authService.getCurrentUser({
                userId: user.id,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                data: userInfo,
                message: '获取用户信息成功'
            });
        } catch (error) {
            request.log.error({
                msg: '获取用户信息失败',
                error: error.message,
                stack: error.stack,
                userId: request.user?.id
            });

            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('获取用户信息失败');
        }
    },

    /**
     * Update current user info
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async updateCurrentUser(request, reply) {
        try {
            const user = request.user;
            const userData = request.body;

            // Call service to update current user info
            await authService.updateCurrentUser({
                userId: user.id,
                userData,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                message: '更新用户信息成功'
            });
        } catch (error) {
            request.log.error({
                msg: '更新用户信息失败',
                error: error.message,
                stack: error.stack,
                userId: request.user?.id,
                body: request.body
            });

            if (error instanceof NOT_FOUND_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('更新用户信息失败');
        }
    },

    /**
     * Change password
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async changePassword(request, reply) {
        try {
            const user = request.user;
            const { oldPassword, newPassword } = request.body;

            // Call service to change password
            await authService.changePassword({
                userId: user.id,
                oldPassword,
                newPassword,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                message: '修改密码成功'
            });
        } catch (error) {
            request.log.error({
                msg: '修改密码失败',
                error: error.message,
                stack: error.stack,
                userId: request.user?.id
            });

            if (error instanceof AUTH_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('修改密码失败');
        }
    },

    /**
     * Reset password
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async resetPassword(request, reply) {
        try {
            const { userId } = request.params;
            const adminUser = request.user;

            // Call service to reset password
            const result = await authService.resetPassword({
                userId,
                adminUserId: adminUser.id,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                data: result,
                message: '重置密码成功'
            });
        } catch (error) {
            request.log.error({
                msg: '重置密码失败',
                error: error.message,
                stack: error.stack,
                params: request.params,
                adminUserId: request.user?.id
            });

            if (error instanceof NOT_FOUND_ERROR || error instanceof AUTH_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('重置密码失败');
        }
    },

    /**
     * Register new user
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async register(request, reply) {
        try {
            const userData = request.body;
            const adminUser = request.user;

            // Call service to register new user
            const result = await authService.register({
                userData,
                adminUserId: adminUser.id,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                data: result,
                message: '注册用户成功'
            });
        } catch (error) {
            request.log.error({
                msg: '注册用户失败',
                error: error.message,
                stack: error.stack,
                body: request.body,
                adminUserId: request.user?.id
            });

            if (error instanceof AUTH_ERROR || error instanceof VALIDATION_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('注册用户失败');
        }
    },

    /**
     * Refresh token
     * @param {Object} request - Fastify request object
     * @param {Object} reply - Fastify reply object
     */
    async refreshToken(request, reply) {
        try {
            const { refreshToken } = request.body;

            // Call service to refresh token
            const result = await authService.refreshToken({
                refreshToken,
                fastify: request.server
            });

            // Return success response
            return reply.success({
                data: result,
                message: '刷新令牌成功'
            });
        } catch (error) {
            request.log.error({
                msg: '刷新令牌失败',
                error: error.message,
                stack: error.stack
            });

            if (error instanceof AUTH_ERROR) {
                throw error;
            }

            throw new INTERNAL_ERROR('刷新令牌失败');
        }
    }
};

export default authController;
