/**
 * Students Utility Functions
 * Contains utility functions for student-related operations
 */

/**
 * Format student data for response
 * @param {Object} student - Student data from database
 * @returns {Object} Formatted student data
 */
export function formatStudentData(student) {
    if (!student) return null;

    return {
        ...student,
        birthday: student.birthday ? Number(student.birthday) : null,
        followUpDate: student.followUpDate ? Number(student.followUpDate) : null,
        createdAt: student.createdAt ? Number(student.createdAt) : null,
        updatedAt: student.updatedAt ? Number(student.updatedAt) : null
    };
}

/**
 * Format student list for response
 * @param {Array} students - Array of student data from database
 * @returns {Array} Formatted student list
 */
export function formatStudentList(students) {
    if (!students || !Array.isArray(students)) return [];

    return students.map(student => formatStudentData(student));
}

/**
 * Calculate student age from birthday
 * @param {string|number} birthday - Birthday timestamp
 * @returns {number} Age in years
 */
export function calculateAge(birthday) {
    if (!birthday) return null;

    const birthDate = new Date(Number(birthday));
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}

/**
 * Generate student ID
 * @returns {string} Generated student ID
 */
export function generateStudentId() {
    return `ST${Date.now()}${Math.floor(Math.random() * 1000)}`;
}

/**
 * Validate student phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} Whether the phone number is valid
 */
export function validatePhone(phone) {
    // Simple validation for demonstration
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

/**
 * Validate student email
 * @param {string} email - Email to validate
 * @returns {boolean} Whether the email is valid
 */
export function validateEmail(email) {
    if (!email) return true; // Email is optional

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate student ID card
 * @param {string} idCard - ID card to validate
 * @returns {boolean} Whether the ID card is valid
 */
export function validateIdCard(idCard) {
    if (!idCard) return true; // ID card is optional

    // Simple validation for demonstration
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCard);
}

/**
 * Get student status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getStatusText(status) {
    const statusMap = {
        'active': '在读',
        'inactive': '休学',
        'deleted': '已删除',
        'graduated': '已毕业'
    };

    return statusMap[status] || status;
}

/**
 * Get student type text
 * @param {string} type - Type code
 * @returns {string} Type text
 */
export function getTypeText(type) {
    const typeMap = {
        'formal': '正式学员',
        'intent': '意向学员',
        'public': '公海学员',
        'graduated': '毕业学员'
    };

    return typeMap[type] || type;
}

/**
 * Filter students by criteria
 * @param {Array} students - Array of student data
 * @param {Object} criteria - Filter criteria
 * @returns {Array} Filtered student list
 */
export function filterStudents(students, criteria) {
    if (!students || !Array.isArray(students)) return [];
    if (!criteria) return students;

    return students.filter(student => {
        // Filter by name
        if (criteria.name && !student.name.includes(criteria.name)) {
            return false;
        }

        // Filter by phone
        if (criteria.phone && !student.phone.includes(criteria.phone)) {
            return false;
        }

        // Filter by status
        if (criteria.status && student.status !== criteria.status) {
            return false;
        }

        // Filter by type
        if (criteria.type && student.type !== criteria.type) {
            return false;
        }

        // Filter by intention level
        if (criteria.intentionLevel && student.intentionLevel !== criteria.intentionLevel) {
            return false;
        }

        // Filter by follow-up person
        if (criteria.followUpPerson && student.followUpPerson !== criteria.followUpPerson) {
            return false;
        }

        return true;
    });
}

/**
 * Sort students by field
 * @param {Array} students - Array of student data
 * @param {string} field - Field to sort by
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted student list
 */
export function sortStudents(students, field, order = 'asc') {
    if (!students || !Array.isArray(students)) return [];
    if (!field) return students;

    return [...students].sort((a, b) => {
        let valueA = a[field];
        let valueB = b[field];

        // Handle date fields
        if (field === 'birthday' || field === 'followUpDate' || field === 'createdAt' || field === 'updatedAt') {
            valueA = valueA ? Number(valueA) : 0;
            valueB = valueB ? Number(valueB) : 0;
        }

        // Handle string fields
        if (typeof valueA === 'string') {
            valueA = valueA.toLowerCase();
        }
        if (typeof valueB === 'string') {
            valueB = valueB.toLowerCase();
        }

        // Sort by the field
        if (valueA < valueB) return order === 'asc' ? -1 : 1;
        if (valueA > valueB) return order === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Generate cache key for student queries
 * @param {string} institutionId - Institution ID
 * @param {string} search - Search term
 * @param {number} page - Page number
 * @param {number} pageSize - Page size
 * @returns {string} Cache key
 */
export function generateCacheKey(institutionId, search, page, pageSize) {
    const searchHash = search
        ? require('crypto').createHash('md5').update(search).digest('hex').substring(0, 8)
        : 'none';

    return `students:${institutionId}:${searchHash}:${page}:${pageSize}`;
}

/**
 * Format student product data
 * @param {Object} product - Student product data
 * @returns {Object} Formatted student product data
 */
export function formatStudentProductData(product) {
    if (!product) return null;

    return {
        ...product,
        startDate: product.startDate ? Number(product.startDate) : null,
        endDate: product.endDate ? Number(product.endDate) : null,
        totalSessionCount: Number(product.totalSessionCount || 0),
        remainingSessionCount: Number(product.remainingSessionCount || 0),
        sessionUnitPrice: Number(product.sessionUnitPrice || 0),
        remainingBalance: Number(product.remainingBalance || 0)
    };
}

/**
 * Format student product list
 * @param {Array} products - Array of student product data
 * @returns {Array} Formatted student product list
 */
export function formatStudentProductList(products) {
    if (!products || !Array.isArray(products)) return [];

    return products.map(product => formatStudentProductData(product));
}

/**
 * Get enrollment status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getEnrollmentStatusText(status) {
    const statusMap = {
        'active': '有效',
        'expired': '已过期',
        'used': '已用完',
        'cancelled': '已取消'
    };

    return statusMap[status] || status;
}

/**
 * Calculate remaining days for a student product
 * @param {number} endDate - End date timestamp
 * @returns {number} Remaining days
 */
export function calculateRemainingDays(endDate) {
    if (!endDate) return 0;

    const now = new Date().getTime();
    const diff = endDate - now;

    if (diff <= 0) return 0;

    return Math.ceil(diff / (1000 * 60 * 60 * 24));
}

/**
 * Check if a student product is expired
 * @param {number} endDate - End date timestamp
 * @returns {boolean} Whether the product is expired
 */
export function isProductExpired(endDate) {
    if (!endDate) return false;

    const now = new Date().getTime();
    return now > endDate;
}

export default {
    formatStudentData,
    formatStudentList,
    calculateAge,
    generateStudentId,
    validatePhone,
    validateEmail,
    validateIdCard,
    getStatusText,
    getTypeText,
    filterStudents,
    sortStudents,
    generateCacheKey,
    formatStudentProductData,
    formatStudentProductList,
    getEnrollmentStatusText,
    calculateRemainingDays,
    isProductExpired
};
