import fp from "fastify-plugin";
import pg from "@fastify/postgres";

async function postgresConnector(fastify, options) {
    try {
        const connectionString = `postgres://${fastify.config.POSTGRES_USER}:${fastify.config.POSTGRES_PASSWORD}@${fastify.config.POSTGRES_HOST}/${fastify.config.POSTGRES_DB}`;

        await fastify.register(pg, {
            connectionString,
            max: 20, // 增加连接池最大连接数以支持更高并发
            min: 2, // 最小保持连接数，减少冷启动时间
            idleTimeoutMillis: 30000, // 连接空闲超时时间
            connectionTimeoutMillis: 5000, // 增加连接超时时间，防止高负载时连接失败
            statement_timeout: 10000, // 语句执行超时时间（毫秒）
            query_timeout: 10000, // 查询超时时间（毫秒）
            allowExitOnIdle: false // 防止空闲时自动退出
        });

        // 直接测试 pool 连接，而不是手动获取 client
        const { rows } = await fastify.pg.query('SELECT NOW()');
        fastify.log.info({ actor: 'Postgres', time: rows[0].now }, 'connected');

    } catch (error) {
        fastify.log.error({ actor: 'Postgres' }, `connection error: ${error}`);
        process.exit(1);
    }
}

export default fp(postgresConnector, {
    name: 'postgres-connector',
    dependencies: ['env-plugin']
});
