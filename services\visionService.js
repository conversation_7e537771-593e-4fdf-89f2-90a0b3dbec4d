import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';

/**
 * 分析图像
 * @param {string} imageUrl - 图像URL
 * @param {string} analysisType - 分析类型 (general, objects, faces, text, nsfw)
 * @returns {Promise<Object>} 分析结果
 */
export async function analyzeImage(imageUrl, analysisType = 'general') {
    try {
        const startTime = Date.now();
        
        // 下载图像
        const imageResponse = await fetch(imageUrl);
        if (!imageResponse.ok) {
            throw new Error(`下载图像失败: ${imageResponse.statusText}`);
        }
        
        // 获取图像数据
        const imageBuffer = await imageResponse.buffer();
        
        // 根据分析类型选择不同的API端点
        let apiEndpoint = 'https://qianfan.baidubce.com/v1/vision/analysis';
        let apiParams = { type: 'general' };
        
        switch (analysisType) {
            case 'objects':
                apiParams.type = 'object_detection';
                break;
            case 'faces':
                apiParams.type = 'face_detection';
                break;
            case 'text':
                apiEndpoint = 'https://qianfan.baidubce.com/v1/vision/ocr';
                apiParams.type = 'general';
                break;
            case 'nsfw':
                apiParams.type = 'nsfw_detection';
                break;
            default:
                apiParams.type = 'general';
        }
        
        // 调用百度文心千帆视觉API
        const response = await fetch(apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                image: imageBuffer.toString('base64'),
                ...apiParams
            })
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // 计算处理时间
        const processTime = Date.now() - startTime;
        console.log(`图像分析完成，用时 ${processTime} 毫秒`);
        
        // 根据分析类型处理结果
        let result;
        switch (analysisType) {
            case 'objects':
                result = {
                    objects: data.objects || [],
                    count: data.objects?.length || 0
                };
                break;
            case 'faces':
                result = {
                    faces: data.faces || [],
                    count: data.faces?.length || 0
                };
                break;
            case 'text':
                result = {
                    text: data.text || '',
                    lines: data.lines || [],
                    words: data.words || []
                };
                break;
            case 'nsfw':
                result = {
                    isNSFW: data.nsfw_score > 0.5,
                    score: data.nsfw_score || 0
                };
                break;
            default:
                result = {
                    categories: data.categories || [],
                    tags: data.tags || [],
                    description: data.description || ''
                };
        }
        
        return {
            id: data.id || uuidv4(),
            created: data.created || new Date().toISOString(),
            analysisType,
            result
        };
    } catch (error) {
        throw new Error(`图像分析失败: ${error.message}`);
    }
}

/**
 * 生成图像描述
 * @param {string} imageUrl - 图像URL
 * @param {string} language - 描述语言 (zh, en)
 * @param {string} detailLevel - 描述详细程度 (basic, detailed)
 * @returns {Promise<Object>} 描述结果
 */
export async function captionImage(imageUrl, language = 'zh', detailLevel = 'detailed') {
    try {
        const startTime = Date.now();
        
        // 下载图像
        const imageResponse = await fetch(imageUrl);
        if (!imageResponse.ok) {
            throw new Error(`下载图像失败: ${imageResponse.statusText}`);
        }
        
        // 获取图像数据
        const imageBuffer = await imageResponse.buffer();
        
        // 构建提示词
        const langPrompt = language === 'zh' ? '中文' : 'English';
        const detailPrompt = detailLevel === 'detailed' ? '详细' : '简短';
        const prompt = language === 'zh' 
            ? `请提供一段${detailPrompt}的${langPrompt}描述，说明这张图片中的内容。` 
            : `Please provide a ${detailLevel} ${langPrompt} description of what is in this image.`;
        
        // 调用百度文心千帆多模态API
        const response = await fetch('https://qianfan.baidubce.com/v1/multimodal/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.BAIDU_API_KEY}`
            },
            body: JSON.stringify({
                model: 'ERNIE-Bot-Vision',
                messages: [
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: prompt
                            },
                            {
                                type: 'image',
                                image_data: {
                                    data: imageBuffer.toString('base64')
                                }
                            }
                        ]
                    }
                ]
            })
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.choices || data.choices.length === 0) {
            throw new Error('未返回有效描述内容');
        }
        
        // 计算处理时间
        const processTime = Date.now() - startTime;
        console.log(`图像描述生成完成，用时 ${processTime} 毫秒`);
        
        return {
            id: data.id || uuidv4(),
            created: data.created || new Date().toISOString(),
            caption: data.choices[0].message.content,
            language,
            detailLevel
        };
    } catch (error) {
        throw new Error(`图像描述生成失败: ${error.message}`);
    }
}
