// 购买记录 schema
export const buyingRecordSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number', default: 1 },
      pageSize: { type: 'number', default: 10 },
      teacher: { type: 'string' },
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      search: { type: 'string' },
    },
  }
};

// 授课记录 schema
export const teachingRecordSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      search: { type: 'string' },
    },
  }
};

// 销售概览 schema
export const salesOverviewSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      checkType: {
        type: 'string',
        default: 'daily',
        enum: ['daily', 'month'],
        description: 'daily: 日报表， month: 月报表',
      }
    }
  }
};

// 销售人员概览 schema
export const salesrepOverviewSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      page: {
        type: 'number',
        default: 1,
      },
      pageSize: {
        type: 'number',
        default: 10,
      },
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      teacher: {
        type: 'string',
        description: 'Filter by salesRep ID'
      }
    }
  }
};

// 学员概览 schema
export const studentOverviewSchema = {
  tags: ['finance']
};

// 教育概览 schema
export const educationOverviewSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      checkType: {
        type: 'string',
        default: 'daily',
        enum: ['daily', 'monthly']
      }
    }
  }
};

// 学员费用 schema
export const studentFeesSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number', default: 1 },
      pageSize: { type: 'number', default: 10 },
      search: { type: 'string' },
    }
  }
};

// 学员消课汇总 schema
export const studentClassSummarySchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      checkType: {
        type: 'string',
        default: 'daily',
        enum: ['daily', 'monthly']
      }
    }
  }
};

// 退款记录 schema
export const refundRecordsSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      page: { type: 'number' },
      pageSize: { type: 'number' },
      search: { type: 'string' },
      operator: { type: 'string' }
    }
  }
};

// 机构收入 schema
export const institutionIncomeSchema = {
  tags: ['finance'],
  querystring: {
    type: 'object',
    properties: {
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      checkType: {
        type: 'string',
        default: 'daily',
        enum: ['daily', 'monthly']
      }
    }
  }
}; 