import roleController from '../controllers/roleController.js';
import roleSchema from '../schemas/role.js';

/**
 * Role routes
 * @param {Object} fastify - Fastify instance
 */
export default async function (fastify) {
    // 获取所有角色列表
    fastify.get("/roles", {
        schema: roleSchema.getAllRolesSchema,
        onRequest: [
            fastify.auth.authenticate,
            // Uncomment to add permission check
            // fastify.auth.requirePermission('system:role:view')
        ],
        handler: roleController.getAllRoles
    });

    // 创建新角色
    fastify.post("/roles", {
        schema: roleSchema.createRoleSchema,
        onRequest: [
            fastify.auth.authenticate,
            // Uncomment to add permission check
            // fastify.auth.requirePermission('system:role:create')
        ],
        handler: roleController.createRole
    });

    // 根据角色id删除
    fastify.delete('/roles/:roleId', {
        schema: roleSchema.deleteRoleSchema,
        onRequest: [
            fastify.auth.authenticate,
            // Uncomment to add permission check
            // fastify.auth.requirePermission('system:role:delete')
        ],
        handler: roleController.deleteRole
    });

    // 根据角色id更新
    fastify.put('/roles/:roleId', {
        schema: roleSchema.updateRoleSchema,
        onRequest: [
            fastify.auth.authenticate,
            // Uncomment to add permission check
            // fastify.auth.requirePermission('system:role:update')
        ],
        handler: roleController.updateRole
    });

    // 获取角色权限
    fastify.get("/roles/:roleId/permissions", {
        schema: roleSchema.getRolePermissionsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: roleController.getRolePermissions
    });

    // 给角色分配权限
    fastify.post('/roles/:roleId/permissions', {
        schema: roleSchema.assignRolePermissionsSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:role:assign-permissions')
        ],
        handler: roleController.assignRolePermissions
    });

    // 根据角色id更新权限菜单
    fastify.put('/roles/:roleId/permissions', {
        schema: roleSchema.updateRolePermissionsSchema,
        onRequest: [
            fastify.auth.authenticate,
            // fastify.auth.requirePermission('system:role:assign-permissions')
        ],
        handler: roleController.updateRolePermissions
    });

    // 移除角色权限
    fastify.delete('/roles/:roleId/permissions', {
        schema: roleSchema.removeRolePermissionsSchema,
        onRequest: [
            fastify.auth.authenticate,
            // fastify.auth.requirePermission('system:role:assign-permissions')
        ],
        handler: roleController.removeRolePermissions
    });
}