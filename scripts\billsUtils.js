/**
 * Bills Utility Functions
 * Contains utility functions for bill-related operations
 */

/**
 * Format bill data for response
 * @param {Object} billData - Bill data from database
 * @returns {Object} Formatted bill data
 */
export function formatBillData(billData) {
    if (!billData) return null;
    
    return {
        ...billData,
        paymentTime: Number(billData.paymentTime),
        operatorTime: Number(billData.operatorTime),
        amount: Number(billData.amount)
    };
}

/**
 * Format bill list for response
 * @param {Array} bills - Array of bill data from database
 * @returns {Array} Formatted bill list
 */
export function formatBillList(bills) {
    if (!bills || !Array.isArray(bills)) return [];
    
    return bills.map(bill => formatBillData(bill));
}

/**
 * Get bill type text
 * @param {string} billType - Bill type code
 * @returns {string} Bill type text
 */
export function getBillTypeText(billType) {
    const billTypeMap = {
        'income': '收入',
        'expense': '支出'
    };
    
    return billTypeMap[billType] || billType;
}

/**
 * Get bill status text
 * @param {string} status - Status code
 * @returns {string} Status text
 */
export function getBillStatusText(status) {
    const statusMap = {
        'pending': '待处理',
        'completed': '已完成',
        'failed': '失败',
        'refunded': '已退款'
    };
    
    return statusMap[status] || status;
}

/**
 * Get payment method text
 * @param {string} paymentMethod - Payment method code
 * @returns {string} Payment method text
 */
export function getPaymentMethodText(paymentMethod) {
    const paymentMethodMap = {
        'cash': '现金',
        'wechat': '微信',
        'alipay': '支付宝',
        'card': '银行卡',
        'transfer': '转账'
    };
    
    return paymentMethodMap[paymentMethod] || paymentMethod;
}

/**
 * Format date string (YYYY-MM-DD)
 * @param {number} timestamp - Date timestamp
 * @returns {string} Formatted date string
 */
export function formatDateString(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(Number(timestamp));
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

/**
 * Format datetime string (YYYY-MM-DD HH:MM:SS)
 * @param {number} timestamp - Date timestamp
 * @returns {string} Formatted datetime string
 */
export function formatDatetimeString(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(Number(timestamp));
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * Format amount with currency symbol
 * @param {number} amount - Amount
 * @param {string} currency - Currency symbol
 * @returns {string} Formatted amount
 */
export function formatAmount(amount, currency = '¥') {
    if (amount === undefined || amount === null) return '';
    
    return `${currency}${Number(amount).toFixed(2)}`;
}

/**
 * Process chart data
 * @param {Array} bills - Bills data
 * @param {string} type - Chart type ('monthly', 'yearly', 'range')
 * @param {string} label - Data label ('income', 'expense', 'all')
 * @param {number} startTimestamp - Start timestamp
 * @param {number} endTimestamp - End timestamp
 * @param {number} year - Year
 * @param {number} month - Month
 * @param {number} currentYear - Current year
 * @param {number} currentMonth - Current month
 * @returns {Object} Chart data
 */
export function processChartData(
    bills, type, label, startTimestamp, endTimestamp, 
    year, month, currentYear, currentMonth
) {
    // Generate labels
    let labels;
    
    if (type === 'range') {
        const start = new Date(startTimestamp);
        const end = new Date(endTimestamp);
        const daysInRange = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        
        labels = Array.from({ length: daysInRange }, (_, i) => {
            const date = new Date(start);
            date.setDate(start.getDate() + i);
            return `${date.getMonth() + 1}-${date.getDate()}`;
        });
    } else if (type === 'monthly') {
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        labels = Array.from({ length: daysInMonth }, (_, i) => `${i + 1}日`);
    } else { // yearly
        const monthLabels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
        labels = year === currentYear ? monthLabels.slice(0, currentMonth + 1) : monthLabels;
    }
    
    // Initialize datasets
    const datasets = [];
    const dataMap = {};
    
    if (label === 'all' || label === 'expense') {
        const expenseData = new Array(labels.length).fill(0);
        datasets.push({ label: '支出', data: expenseData });
        dataMap['expense'] = expenseData;
    }
    
    if (label === 'all' || label === 'income') {
        const incomeData = new Array(labels.length).fill(0);
        datasets.push({ label: '收入', data: incomeData });
        dataMap['income'] = incomeData;
    }
    
    // Populate data
    bills.forEach(bill => {
        const billTime = Number(bill.paymentTime);
        const billAmount = Number(bill.amount);
        let index;
        
        if (type === 'range') {
            index = Math.floor((billTime - startTimestamp) / (1000 * 60 * 60 * 24));
        } else if (type === 'monthly') {
            index = new Date(billTime).getDate() - 1;
        } else { // yearly
            index = new Date(billTime).getMonth();
        }
        
        if (index >= 0 && index < labels.length && dataMap[bill.billType]) {
            dataMap[bill.billType][index] += billAmount;
        }
    });
    
    return { labels, datasets };
}

/**
 * Calculate total amount
 * @param {Array} bills - Bills data
 * @param {string} billType - Bill type ('income', 'expense', 'all')
 * @returns {number} Total amount
 */
export function calculateTotalAmount(bills, billType = 'all') {
    if (!bills || !Array.isArray(bills)) return 0;
    
    return bills.reduce((total, bill) => {
        if (billType === 'all' || bill.billType === billType) {
            return total + Number(bill.amount);
        }
        return total;
    }, 0);
}

/**
 * Calculate net amount (income - expense)
 * @param {Array} bills - Bills data
 * @returns {number} Net amount
 */
export function calculateNetAmount(bills) {
    if (!bills || !Array.isArray(bills)) return 0;
    
    return bills.reduce((net, bill) => {
        if (bill.billType === 'income') {
            return net + Number(bill.amount);
        } else if (bill.billType === 'expense') {
            return net - Number(bill.amount);
        }
        return net;
    }, 0);
}

/**
 * Filter bills by date range
 * @param {Array} bills - Bills data
 * @param {number} startTime - Start timestamp
 * @param {number} endTime - End timestamp
 * @returns {Array} Filtered bills
 */
export function filterBillsByDateRange(bills, startTime, endTime) {
    if (!bills || !Array.isArray(bills)) return [];
    if (!startTime && !endTime) return bills;
    
    return bills.filter(bill => {
        const paymentTime = Number(bill.paymentTime);
        
        if (startTime && endTime) {
            return paymentTime >= startTime && paymentTime <= endTime;
        } else if (startTime) {
            return paymentTime >= startTime;
        } else if (endTime) {
            return paymentTime <= endTime;
        }
        
        return true;
    });
}

/**
 * Group bills by period
 * @param {Array} bills - Bills data
 * @param {string} period - Period ('day', 'month', 'year')
 * @returns {Object} Grouped bills
 */
export function groupBillsByPeriod(bills, period = 'day') {
    if (!bills || !Array.isArray(bills)) return {};
    
    const groupedBills = {};
    
    bills.forEach(bill => {
        const date = new Date(Number(bill.paymentTime));
        let key;
        
        if (period === 'day') {
            key = formatDateString(bill.paymentTime);
        } else if (period === 'month') {
            key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        } else if (period === 'year') {
            key = `${date.getFullYear()}`;
        }
        
        if (!groupedBills[key]) {
            groupedBills[key] = [];
        }
        
        groupedBills[key].push(bill);
    });
    
    return groupedBills;
}

export default {
    formatBillData,
    formatBillList,
    getBillTypeText,
    getBillStatusText,
    getPaymentMethodText,
    formatDateString,
    formatDatetimeString,
    formatAmount,
    processChartData,
    calculateTotalAmount,
    calculateNetAmount,
    filterBillsByDateRange,
    groupBillsByPeriod
};
