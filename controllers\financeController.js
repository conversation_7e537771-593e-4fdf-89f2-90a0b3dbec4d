import financeService from '../services/finanaceService/financeService.js';

/**
 * Finance controller containing all finance-related route handlers
 */
export default {
  /**
   * Get buying records with pagination and filtering
   */
  async getBuyingRecords(request, reply) {
    const user = request.user;
    const { teacher, page = 1, pageSize = 10, startTime, endTime, search } = request.query;
    
    try {
      const result = await financeService.getBuyingRecords({
        user,
        teacher,
        page,
        pageSize,
        startTime,
        endTime,
        search
      });
      
      reply.success({
        message: 'success',
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get teaching records with filtering
   */
  async getTeachingRecords(request, reply) {
    const user = request.user;
    const { startTime, endTime, search } = request.query;
    
    try {
      const result = await financeService.getTeachingRecords({
        user,
        startTime,
        endTime,
        search
      });
      
      reply.success({
        message: 'success',
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get sales overview data
   */
  async getSalesOverview(request, reply) {
    const user = request.user;
    const { startTime, endTime, checkType = 'daily' } = request.query;
    
    try {
      const result = await financeService.getSalesOverview({
        user,
        startTime,
        endTime,
        checkType
      });
      
      reply.success({
        data: result
      });
    } catch (error) {
      request.log.error(error);
      if (error.message === 'Invalid time range') {
        return reply.code(400).send({
          success: false,
          error: 'Invalid time parameters'
        });
      }
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get salesrep overview data
   */
  async getSalesrepOverview(request, reply) {
    try {
      const user = request.user;
      const { teacher, startTime, endTime, page = 1, pageSize = 10 } = request.query;
      
      const result = await financeService.getSalesrepOverview({
        user,
        teacher,
        startTime,
        endTime,
        page,
        pageSize
      });
      
      reply.success({
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({
        success: false,
        error: 'Failed to retrieve salesrep overview'
      });
    }
  },

  /**
   * Get student overview data
   */
  async getStudentOverview(request, reply) {
    const user = request.user;
    
    try {
      const result = await financeService.getStudentOverview(user);
      
      reply.success({
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get education overview data
   */
  async getEducationOverview(request, reply) {
    const user = request.user;
    const { startTime, endTime, checkType = 'daily' } = request.query;
    
    try {
      const result = await financeService.getEducationOverview({
        user,
        startTime,
        endTime,
        checkType
      });
      
      reply.success({
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get student fees data
   */
  async getStudentFees(request, reply) {
    const user = request.user;
    const { page = 1, pageSize = 10, search } = request.query;
    
    try {
      const result = await financeService.getStudentFees({
        user,
        page,
        pageSize,
        search
      });
      
      reply.success({
        message: 'success',
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get student class summary data
   */
  async getStudentClassSummary(request, reply) {
    const user = request.user;
    const { startTime, endTime, checkType } = request.query;
    
    try {
      const result = await financeService.getStudentClassSummary({
        user,
        startTime,
        endTime,
        checkType
      });
      
      reply.success({
        message: 'success',
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get refund records data
   */
  async getRefundRecords(request, reply) {
    const user = request.user;
    const { startTime, endTime, page = 1, pageSize = 10, operator, search } = request.query;
    
    try {
      const result = await financeService.getRefundRecords({
        user,
        startTime,
        endTime,
        page,
        pageSize,
        operator,
        search
      });
      
      reply.success({
        message: 'success',
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  },

  /**
   * Get institution income data
   */
  async getInstitutionIncome(request, reply) {
    const user = request.user;
    const { startTime, endTime, checkType = 'daily' } = request.query;
    
    try {
      const result = await financeService.getInstitutionIncome({
        user,
        startTime,
        endTime,
        checkType
      });
      
      reply.success({
        data: result
      });
    } catch (error) {
      request.log.error(error);
      reply.code(500).send({ message: 'Internal Server Error' });
    }
  }
};
