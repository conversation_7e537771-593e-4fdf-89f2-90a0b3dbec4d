import wxSend from '../libs/wxSend.js'

export default async function (fastify, options) {
    fastify.post('/wx-test-push-message', {
        handler: async (request, reply) => {
            const data = {
                title: {
                    value: '签到提醒',
                },
                student: {
                    value: '张三',
                },
                attendanceTime: {
                    value: '2025-04-24 10:30:00',
                },
                course: {
                    value: '我爱睡觉',
                },
                classesTime: {
                    value: '2025-04-24(周六) 10:30 - 12:00',
                },
                product: {
                    value: '睡觉套餐',
                },
                quotaLeft: {
                    value: '100次',
                },
                deduct: {
                    value: '1.0',
                },
                teacher: {
                    value: '李四',
                },
                operator: {
                    value: '张三',
                }
                
            }
            const result = await wxSend.wxPushMessageForTeachers(
                'attendance', 'om4y-vhPuo1ICzk4U0ZJFaXzk2FE', data)
            console.log(result, '发送结果1')
            reply.send({
                code: 200,
                message: '发送成功',
                data: result,
            })
        }
    })
}
