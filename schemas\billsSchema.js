/**
 * Bills Schema
 * Defines validation schemas for bill-related endpoints
 */
const billsSchema = {
    /**
     * Schema for getting bills list
     */
    getBillsListSchema: {
        tags: ['bills'],
        summary: '获取账单列表',
        description: '获取账单列表，支持分页和筛选',
        querystring: {
            type: 'object',
            properties: {
                page: { 
                    type: 'number', 
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: { 
                    type: 'number', 
                    default: 10,
                    description: '每页数量，默认10'
                },
                startTime: { 
                    type: 'string',
                    description: '开始时间戳'
                },
                endTime: { 
                    type: 'string',
                    description: '结束时间戳'
                },
                source: { 
                    type: 'string',
                    description: '账单来源'
                },
                operator: { 
                    type: 'string',
                    description: '操作人ID'
                },
                billType: { 
                    type: 'string',
                    enum: ['income', 'expense'],
                    description: '账单类型：收入或支出'
                },
                status: { 
                    type: 'string',
                    enum: ['pending', 'completed', 'failed', 'refunded'],
                    description: '账单状态'
                },
                paymentMethod: { 
                    type: 'string',
                    description: '支付方式'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        amount: { type: 'number' },
                                        billType: { type: 'string' },
                                        status: { type: 'string' },
                                        source: { type: 'string' },
                                        paymentMethod: { type: 'string' },
                                        paymentTime: { type: 'number' },
                                        operatorTime: { type: 'number' },
                                        remarks: { type: 'string' },
                                        operator: {
                                            type: 'object',
                                            properties: {
                                                id: { type: 'string' },
                                                name: { type: 'string' }
                                            }
                                        },
                                        student: {
                                            type: 'object',
                                            properties: {
                                                id: { type: 'string' },
                                                name: { type: 'string' },
                                                phone: { type: 'string' }
                                            }
                                        },
                                        product: {
                                            type: 'object',
                                            properties: {
                                                id: { type: 'string' },
                                                name: { type: 'string' },
                                                price: { type: 'number' }
                                            }
                                        }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for creating bill
     */
    createBillSchema: {
        tags: ['bills'],
        summary: '创建账单',
        description: '创建新账单',
        body: {
            type: 'object',
            required: ['amount', 'type'],
            properties: {
                amount: { 
                    type: 'number',
                    description: '金额'
                },
                paymentMethod: { 
                    type: 'string',
                    description: '支付方式'
                },
                paymentTime: { 
                    type: 'string',
                    description: '支付时间戳'
                },
                operator: { 
                    type: 'string',
                    description: '操作人ID'
                },
                studentId: { 
                    type: 'string',
                    description: '学生ID'
                },
                productId: { 
                    type: 'string',
                    description: '产品ID'
                },
                source: { 
                    type: 'string',
                    description: '账单来源'
                },
                type: { 
                    type: 'string',
                    enum: ['income', 'expense'],
                    description: '账单类型：收入或支出'
                },
                remarks: { 
                    type: 'string',
                    description: '备注'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting bill statistics
     */
    getBillStatisticsSchema: {
        tags: ['bills'],
        summary: '获取账单统计',
        description: '获取账单统计数据',
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            today: {
                                type: 'object',
                                properties: {
                                    income: { type: 'number' },
                                    expense: { type: 'number' },
                                    net: { type: 'number' }
                                }
                            },
                            yesterday: {
                                type: 'object',
                                properties: {
                                    income: { type: 'number' },
                                    expense: { type: 'number' },
                                    net: { type: 'number' }
                                }
                            },
                            thisMonth: {
                                type: 'object',
                                properties: {
                                    income: { type: 'number' },
                                    expense: { type: 'number' },
                                    net: { type: 'number' }
                                }
                            },
                            lastMonth: {
                                type: 'object',
                                properties: {
                                    income: { type: 'number' },
                                    expense: { type: 'number' },
                                    net: { type: 'number' }
                                }
                            },
                            thisYear: {
                                type: 'object',
                                properties: {
                                    income: { type: 'number' },
                                    expense: { type: 'number' },
                                    net: { type: 'number' }
                                }
                            },
                            total: {
                                type: 'object',
                                properties: {
                                    income: { type: 'number' },
                                    expense: { type: 'number' },
                                    net: { type: 'number' }
                                }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting annual data
     */
    getAnnualDataSchema: {
        tags: ['bills'],
        summary: '获取年度数据',
        description: '获取账单年度数据，用于图表展示',
        querystring: {
            type: 'object',
            properties: {
                type: { 
                    type: 'string', 
                    enum: ['monthly', 'yearly', 'range'], 
                    default: 'yearly',
                    description: '数据类型：月度、年度或自定义范围'
                },
                startDate: { 
                    type: 'number',
                    description: '开始日期时间戳（仅在type=range时使用）'
                },
                endDate: { 
                    type: 'number',
                    description: '结束日期时间戳（仅在type=range时使用）'
                },
                label: { 
                    type: 'string', 
                    enum: ['income', 'expense', 'all'], 
                    default: 'all',
                    description: '数据标签：收入、支出或全部'
                },
                year: { 
                    type: 'integer', 
                    nullable: true,
                    description: '年份'
                },
                month: { 
                    type: 'integer', 
                    minimum: 1, 
                    maximum: 12, 
                    nullable: true,
                    description: '月份（1-12）'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            labels: {
                                type: 'array',
                                items: { type: 'string' }
                            },
                            datasets: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        label: { type: 'string' },
                                        data: {
                                            type: 'array',
                                            items: { type: 'number' }
                                        }
                                    }
                                }
                            },
                            metadata: {
                                type: 'object',
                                properties: {
                                    year: { type: 'number' },
                                    month: { type: 'number', nullable: true },
                                    type: { type: 'string' },
                                    isCurrentYear: { type: 'boolean' }
                                }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting bill by ID
     */
    getBillByIdSchema: {
        tags: ['bills'],
        summary: '获取账单详情',
        description: '根据ID获取账单详情',
        params: {
            type: 'object',
            required: ['billId'],
            properties: {
                billId: { 
                    type: 'string',
                    description: '账单ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            amount: { type: 'number' },
                            billType: { type: 'string' },
                            status: { type: 'string' },
                            source: { type: 'string' },
                            paymentMethod: { type: 'string' },
                            paymentTime: { type: 'number' },
                            operatorTime: { type: 'number' },
                            remarks: { type: 'string' },
                            operator: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' }
                                }
                            },
                            student: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' },
                                    phone: { type: 'string' }
                                }
                            },
                            product: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' },
                                    price: { type: 'number' }
                                }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for updating bill
     */
    updateBillSchema: {
        tags: ['bills'],
        summary: '更新账单',
        description: '更新账单信息',
        params: {
            type: 'object',
            required: ['billId'],
            properties: {
                billId: { 
                    type: 'string',
                    description: '账单ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                amount: { type: 'number' },
                billType: { 
                    type: 'string',
                    enum: ['income', 'expense']
                },
                status: { 
                    type: 'string',
                    enum: ['pending', 'completed', 'failed', 'refunded']
                },
                source: { type: 'string' },
                paymentMethod: { type: 'string' },
                paymentTime: { type: 'number' },
                remarks: { type: 'string' }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for deleting bill
     */
    deleteBillSchema: {
        tags: ['bills'],
        summary: '删除账单',
        description: '删除账单',
        params: {
            type: 'object',
            required: ['billId'],
            properties: {
                billId: { 
                    type: 'string',
                    description: '账单ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    }
};

export default billsSchema;
