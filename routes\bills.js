import { BillController } from '../controllers/billController.js';
import { getBillsSchema, createBillSchema, getAnnualDataSchema } from '../schemas/billSchemas.js';

export default function (fastify, options) {
    const billController = new BillController(fastify);

    // 获取账单列表
    fastify.get('/bills', {
        schema: getBillsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => billController.getBills(request, reply)
    });

    // 创建账单
    fastify.post('/bills', {
        schema: createBillSchema,
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => billController.createBill(request, reply)
    });

    // 获取年度数据
    fastify.get('/bills/annual-data', {
        schema: getAnnualDataSchema,
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => billController.getAnnualData(request, reply)
    });
}
