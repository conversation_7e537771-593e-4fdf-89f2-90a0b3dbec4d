import fetch from "node-fetch"
import { redisSystem} from "../config/redis.js"
import fastify from "fastify"
import { wxConfig } from "../config/wx.js"

async function getAccessToken() {
    const appid = wxConfig.appid || ''
    const secret = wxConfig.secret || ''
    if (!appid || !secret) {
        throw new Error('WX_APPID or WX_SECRET is not set')
    }

    const accessToken = await redisSystem.get(`wx:accessToken:${appid}`)
    if (accessToken) {
        return accessToken
    }
    try {
        const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`
        const response = await fetch(url)
        const data = await response.json()
    if (data.access_token) {
        await redisSystem.setex(`wx:accessToken:${appid}`, 7200, `${data.access_token}`)
        return data.access_token
    }
    throw new Error('Failed to get access token')
    } catch (error) {
        fastify.log.error(error, '获取accessToken失败')
        throw new Error('Failed to get access token')
    }
}

/**
 * 
 * @param {*} type  1: 签到推送 2: 新增临时学员 3: 上课提醒
 * @param {*} touser 接收用户
 * @param {*} dataMap  推送数据  {first: {value: '您好，您有新的消息'}}
 * @returns 
 */
async function wxPushMessageForTeachers(type,touser, dataMap) {
    try {
        const accessToken = await getAccessToken()
        const url = `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${accessToken}`
        const data = {
            touser,
            template_id: wxConfig.templateId[type],
            data: dataMap,
        }
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        })
        const result = await response.json()
        return result
    } catch (error) {
        fastify.log.error(error, "发送失败")
        return false

    }

}

export default {
    wxPushMessageForTeachers
}