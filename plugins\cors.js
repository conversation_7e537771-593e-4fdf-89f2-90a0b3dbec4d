import fp from "fastify-plugin";
import cors from "@fastify/cors"

async function corsPlugin(fastify, options) {
    await fastify.register(cors, {
        origin: true,
        methods: ['GET', 'PUT', 'POST', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept'],
        credentials: true,
        maxAge: 86400
    })
}

export default fp(corsPlugin, {
    name: 'cors-plugin'
})