import { test } from 'node:test';
import { strict as assert } from 'node:assert';

// Mock the sendToSpecifiedUser function
const mockSendToSpecifiedUser = async () => true;

// Import the controller
import roleController from '../../controllers/roleController.js';

test('Role Permission Controller', async (t) => {
  await t.test('updateRolePermissions should update role permissions based on menu IDs', async () => {
    // Create mock request and reply objects
    const request = {
      user: {
        id: 'test-user-id',
        institutionId: 'test-institution-id'
      },
      params: {
        roleId: 'test-role-id'
      },
      body: {
        permissions: ['menu1', 'menu3']
      },
      server: {
        prisma: {
          $transaction: async (callback) => {
            return await callback({
              role: {
                findUnique: async () => ({
                  id: 'test-role-id',
                  rolePermissions: [
                    { permission: { id: 'perm1', code: 'test:permission1' } },
                    { permission: { id: 'perm2', code: 'test:permission2' } }
                  ]
                })
              },
              menu: {
                findMany: async () => [
                  { id: 'menu1', permissionCode: 'test:permission1' },
                  { id: 'menu3', permissionCode: 'test:permission3' }
                ]
              },
              permission: {
                findMany: async () => [
                  { id: 'perm3' }
                ]
              },
              rolePermission: {
                deleteMany: async () => ({ count: 1 }),
                createMany: async () => ({ count: 1 })
              },
              userRole: {
                findMany: async () => [
                  { userId: 'user1' },
                  { userId: 'user2' }
                ]
              }
            });
          }
        },
        // Mock the sendToSpecifiedUser function
        sendToSpecifiedUser: mockSendToSpecifiedUser
      },
      log: {
        error: () => {},
        warn: () => {},
        info: () => {}
      }
    };

    const reply = {
      success: (data) => {
        return {
          statusCode: 200,
          ...data
        };
      }
    };

    // Call the controller method
    const result = await roleController.updateRolePermissions(request, reply);

    // Assert the result
    assert.equal(result.statusCode, 200);
    assert.equal(result.message, '角色权限菜单更新成功');
    assert.equal(result.data.roleId, 'test-role-id');
    assert.equal(result.data.updatedMenus, 2);
    assert.equal(result.data.removedPermissions, 1);
    assert.equal(result.data.addedPermissions, 1);
  });
});
