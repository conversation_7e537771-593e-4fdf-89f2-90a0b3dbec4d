import fetch from 'node-fetch'

/**
 * 获取微信用户信息
 * POST /api/wechat/get-user-info
 */
export async function getUserWechatInfo(fastify) {
  fastify.post('/wechat/get-user-info', {
    schema: {
      body: {
        type: 'object',
        required: ['code'],
        properties: {
          code: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                openid: { type: 'string' },
                unionid: { type: 'string' },
                nickname: { type: 'string' },
                headimgurl: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { code } = request.body
      
      // 1. 使用code换取access_token
      const tokenResponse = await fetch(`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${process.env.WX_APPID}&secret=${process.env.WX_SECRET}&code=${code}&grant_type=authorization_code`)
      const tokenData = await tokenResponse.json()
      console.log(tokenData, 'tokenData')
      if (tokenData.errcode) {
        throw new Error(tokenData.errmsg || '获取access_token失败')
      }
      
      // 2. 使用access_token获取用户信息
      const userResponse = await fetch(`https://api.weixin.qq.com/sns/userinfo?access_token=${tokenData.access_token}&openid=${tokenData.openid}&lang=zh_CN`)
      const userData = await userResponse.json()
      console.log(userData, 'userData')
      if (userData.errcode) {
        throw new Error(userData.errmsg || '获取用户信息失败')
      }
      
      return {
        success: true,
        data: {
          openid: userData.openid,
          unionid: userData.unionid,
          nickname: userData.nickname,
          headimgurl: userData.headimgurl
        },
        message: '获取成功'
      }
      
    } catch (error) {
      fastify.log.error('获取微信用户信息失败:', error)
      return reply.code(500).send({
        success: false,
        message: error.message || '服务器错误'
      })
    }
  })
}

/**
 * 检查用户是否关注公众号
 * POST /api/wechat/check-subscription
 */
export async function checkSubscriptionStatus(fastify) {
  fastify.post('/wechat/check-subscription', {
    schema: {
      body: {
        type: 'object',
        required: ['openid'],
        properties: {
          openid: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                isSubscribed: { type: 'boolean' },
                subscribeTime: { type: ['number', 'null'] }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { openid } = request.body
      
      // 1. 获取公众号access_token
      const tokenResponse = await fetch(`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${process.env.WX_APPID}&secret=${process.env.WX_SECRET}`)
      const tokenData = await tokenResponse.json()
      
      if (tokenData.errcode) {
        throw new Error(tokenData.errmsg || '获取公众号access_token失败')
      }
      
      // 2. 获取用户基本信息
      const userResponse = await fetch(`https://api.weixin.qq.com/cgi-bin/user/info?access_token=${tokenData.access_token}&openid=${openid}&lang=zh_CN`)
      const userData = await userResponse.json()
      
      // 如果用户未关注，subscribe字段为0
      const isSubscribed = userData.subscribe === 1
      
      return {
        success: true,
        data: {
          isSubscribed,
          subscribeTime: userData.subscribe_time || null
        },
        message: '检查成功'
      }
      
    } catch (error) {
      fastify.log.error('检查关注状态失败:', error)
      return reply.code(500).send({
        success: false,
        message: error.message || '服务器错误'
      })
    }
  })
}

export default async function(fastify, opts) {
  await getUserWechatInfo(fastify)
  await checkSubscriptionStatus(fastify)
} 