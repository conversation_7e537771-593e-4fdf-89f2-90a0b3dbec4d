import fp from "fastify-plugin";
import { createBullBoard } from '@bull-board/api';
import { FastifyAdapter } from '@bull-board/fastify';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter.js';
import { imageQueue, textQueue, speechQueue, visionQueue } from '../queues/index.js';

async function bullmqPlugin(fastify, options) {
    fastify.register(async (fastify) => {
        const serverAdapter = new FastifyAdapter();
        createBullBoard({
            queues: [
                new BullMQAdapter(imageQueue),
                new BullMQAdapter(textQueue),
                new BullMQAdapter(speechQueue),
                new BullMQAdapter(visionQueue)
            ],
            serverAdapter,
        });
        serverAdapter.setBasePath('/admin/queues');
        fastify.register(serverAdapter.registerPlugin(),
        {
            prefix: '/admin/queues',
            options: {
                ui: {
                    title: 'BullMQ Admin',
                    enable: true,
                }
            }
        });
    });
}

export default fp(bullmqPlugin, {
    name: 'bullmq-plugin'
})