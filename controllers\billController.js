import billService from '../services/billService.js';

export class Bill<PERSON>ontroller {
    constructor(fastify) {
        this.fastify = fastify;
        this.billService = billService;
    }

    /**
     * 获取账单列表
     */
    getBills = async (request, reply) => {
        try {
            const { page = 1, pageSize = 10, search, startTime, endTime } = request.query;
            const result = await this.billService.getBills({
                user: request.user,
                page,
                pageSize,
                search,
                startTime,
                endTime
            });
            return reply.success({
                data: result,
                message: '获取账单列表成功'
            });
        } catch (error) {
            request.log.error(error);
            return reply.status(500).send({ message: '获取账单列表失败' });
        }
    }

    /**
     * 创建账单
     */
    createBill = async (request, reply) => {
        try {
            const result = await this.billService.createBill({
                user: request.user,
                ...request.body
            });
            return reply.send(result);
        } catch (error) {
            request.log.error(error);
            return reply.status(500).send({ message: '创建账单失败' });
        }
    }

    /**
     * 获取年度数据
     */
    getAnnualData = async (request, reply) => {
        try {
            const result = await this.billService.getAnnualData({
                user: request.user,
                ...request.query
            });

            return reply.success({
                data: result,
                message: "获取数据成功"
            });
        } catch (error) {
            request.log.error(error);
            return reply.status(500).send({ 
                message: '获取数据失败',
                code: 500
            });
        }
    }
} 