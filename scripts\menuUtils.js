/**
 * Menu Utility Functions
 * Contains utility functions for menu-related operations
 */

/**
 * Format menu data for response
 * @param {Object} menuData - Menu data from database
 * @returns {Object} Formatted menu data
 */
export function formatMenuData(menuData) {
    if (!menuData) return null;
    
    return {
        ...menuData,
        hidden: <PERSON><PERSON><PERSON>(menuData.hidden)
    };
}

/**
 * Format menu list for response
 * @param {Array} menus - Array of menu data from database
 * @returns {Array} Formatted menu list
 */
export function formatMenuList(menus) {
    if (!menus || !Array.isArray(menus)) return [];
    
    return menus.map(menu => formatMenuData(menu));
}

/**
 * Sort menu tree by sort field
 * @param {Array} menuTree - Menu tree
 * @returns {Array} Sorted menu tree
 */
export function sortMenuTree(menuTree) {
    if (!menuTree || !Array.isArray(menuTree)) return [];
    
    // Sort current level
    menuTree.sort((a, b) => a.sort - b.sort);
    
    // Sort children recursively
    menuTree.forEach(menu => {
        if (menu.children && Array.isArray(menu.children)) {
            sortMenuTree(menu.children);
        }
    });
    
    return menuTree;
}

/**
 * Find menu by ID in menu tree
 * @param {Array} menuTree - Menu tree
 * @param {string} menuId - Menu ID
 * @returns {Object|null} Found menu or null
 */
export function findMenuById(menuTree, menuId) {
    if (!menuTree || !Array.isArray(menuTree) || !menuId) return null;
    
    for (const menu of menuTree) {
        if (menu.id === menuId) {
            return menu;
        }
        
        if (menu.children && Array.isArray(menu.children)) {
            const found = findMenuById(menu.children, menuId);
            if (found) {
                return found;
            }
        }
    }
    
    return null;
}

/**
 * Find parent menu by child ID in menu tree
 * @param {Array} menuTree - Menu tree
 * @param {string} childId - Child menu ID
 * @returns {Object|null} Found parent menu or null
 */
export function findParentMenu(menuTree, childId) {
    if (!menuTree || !Array.isArray(menuTree) || !childId) return null;
    
    for (const menu of menuTree) {
        if (menu.children && Array.isArray(menu.children)) {
            for (const child of menu.children) {
                if (child.id === childId) {
                    return menu;
                }
            }
            
            const found = findParentMenu(menu.children, childId);
            if (found) {
                return found;
            }
        }
    }
    
    return null;
}

/**
 * Get menu path (breadcrumb) by menu ID
 * @param {Array} menuTree - Menu tree
 * @param {string} menuId - Menu ID
 * @returns {Array} Menu path
 */
export function getMenuPath(menuTree, menuId) {
    if (!menuTree || !Array.isArray(menuTree) || !menuId) return [];
    
    const path = [];
    
    function findPath(tree, id, currentPath) {
        for (const menu of tree) {
            // Create a new path array with the current menu
            const newPath = [...currentPath, menu];
            
            // If this is the menu we're looking for, return the path
            if (menu.id === id) {
                path.push(...newPath);
                return true;
            }
            
            // If this menu has children, search them
            if (menu.children && Array.isArray(menu.children)) {
                if (findPath(menu.children, id, newPath)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    findPath(menuTree, menuId, []);
    
    return path;
}

/**
 * Get all menu IDs from menu tree
 * @param {Array} menuTree - Menu tree
 * @returns {Array} Menu IDs
 */
export function getAllMenuIds(menuTree) {
    if (!menuTree || !Array.isArray(menuTree)) return [];
    
    const ids = [];
    
    function collectIds(tree) {
        for (const menu of tree) {
            ids.push(menu.id);
            
            if (menu.children && Array.isArray(menu.children)) {
                collectIds(menu.children);
            }
        }
    }
    
    collectIds(menuTree);
    
    return ids;
}

/**
 * Get all leaf menu IDs from menu tree
 * @param {Array} menuTree - Menu tree
 * @returns {Array} Leaf menu IDs
 */
export function getLeafMenuIds(menuTree) {
    if (!menuTree || !Array.isArray(menuTree)) return [];
    
    const ids = [];
    
    function collectLeafIds(tree) {
        for (const menu of tree) {
            if (!menu.children || !Array.isArray(menu.children) || menu.children.length === 0) {
                ids.push(menu.id);
            } else {
                collectLeafIds(menu.children);
            }
        }
    }
    
    collectLeafIds(menuTree);
    
    return ids;
}

/**
 * Filter menu tree by permission codes
 * @param {Array} menuTree - Menu tree
 * @param {Set} permissionCodes - Permission codes
 * @returns {Array} Filtered menu tree
 */
export function filterMenuTreeByPermissions(menuTree, permissionCodes) {
    if (!menuTree || !Array.isArray(menuTree)) return [];
    
    return menuTree.filter(menu => {
        // Keep menu if it has no permission code or the user has the permission
        const keepMenu = !menu.permissionCode || permissionCodes.has(menu.permissionCode);
        
        // Filter children recursively
        if (menu.children && Array.isArray(menu.children)) {
            menu.children = filterMenuTreeByPermissions(menu.children, permissionCodes);
        }
        
        // Keep menu if it has no permission code, the user has the permission, or it has visible children
        return keepMenu || (menu.children && menu.children.length > 0);
    });
}

/**
 * Remove hidden menus from menu tree
 * @param {Array} menuTree - Menu tree
 * @returns {Array} Visible menu tree
 */
export function removeHiddenMenus(menuTree) {
    if (!menuTree || !Array.isArray(menuTree)) return [];
    
    return menuTree.filter(menu => {
        // Filter children recursively
        if (menu.children && Array.isArray(menu.children)) {
            menu.children = removeHiddenMenus(menu.children);
        }
        
        // Keep menu if it's not hidden or it has visible children
        return !menu.hidden || (menu.children && menu.children.length > 0);
    });
}

/**
 * Get menu tree depth
 * @param {Array} menuTree - Menu tree
 * @returns {number} Menu tree depth
 */
export function getMenuTreeDepth(menuTree) {
    if (!menuTree || !Array.isArray(menuTree) || menuTree.length === 0) return 0;
    
    let maxDepth = 1;
    
    for (const menu of menuTree) {
        if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
            const childDepth = getMenuTreeDepth(menu.children);
            maxDepth = Math.max(maxDepth, childDepth + 1);
        }
    }
    
    return maxDepth;
}

export default {
    formatMenuData,
    formatMenuList,
    sortMenuTree,
    findMenuById,
    findParentMenu,
    getMenuPath,
    getAllMenuIds,
    getLeafMenuIds,
    filterMenuTreeByPermissions,
    removeHiddenMenus,
    getMenuTreeDepth
};
