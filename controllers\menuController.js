import menuService from '../services/menuService.js';

async function getUserMenus(request, reply) {
    try {
        const user = request.user;
        const menuTree = await menuService.getUserMenus(request.server, user);
        
        reply.success({
            message: '获取用户菜单成功.',
            data: menuTree
        });
    } catch (error) {
        throw error;
    }
}

async function getAllMenus(request, reply) {
    try {
        const user = request.user;
        const menuTree = await menuService.getAllMenus(request.server, user);
        
        reply.success({
            message: '获取菜单成功.',
            data: menuTree
        });
    } catch (error) {
        throw error;
    }
}

async function getRoleMenus(request, reply) {
    try {
        const roleId = request.params.roleId;
        const menuTree = await menuService.getRoleMenus(request.server, roleId);
        
        reply.success({
            message: '获取角色菜单成功.',
            data: menuTree
        });
    } catch (error) {
        throw error;
    }
}

export default {
    getUserMenus,
    getAllMenus,
    getRoleMenus
}