import { Redis } from "ioredis";

export const redisConfig = {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD || '',
    maxRetriesPerRequest: 5, // 增加每个请求的最大重试次数
    connectTimeout: 15000, // 增加连接超时时间（毫秒）
    commandTimeout: 10000, // 增加命令超时时间（毫秒）
    retryStrategy: (times) => {
        // 改进的指数退避重试策略
        const delay = Math.min(Math.pow(2, times) * 50, 5000);
        return delay;
    },
    enableReadyCheck: true, // 启用就绪检查
    enableOfflineQueue: true, // 启用离线队列
    enableAutoPipelining: false, // 默认禁用自动管道化，仅在特定实例启用
    autoResendUnfulfilledCommands: true, // 自动重发未完成的命令
    reconnectOnError: (err) => {
        // 在特定错误情况下重连
        const targetErrors = ['READONLY', 'ETIMEDOUT', 'ECONNRESET', 'ECONNREFUSED'];
        return targetErrors.some(e => err.message.includes(e));
    },
    lazyConnect: false, // 立即连接而不是延迟连接
    sentinelRetryStrategy: (times) => {
        // Sentinel重试策略
        return Math.min(times * 100, 3000);
    }
}


// 创建Redis连接池
const createRedisClient = (db, keyPrefix, options = {}) => {
    return new Redis({
        ...redisConfig,
        db,
        keyPrefix,
        // 连接事件处理
        reconnectOnError: (err) => {
            const targetError = 'READONLY';
            if (err.message.includes(targetError)) {
                // 只有在READONLY错误时才重连
                return true;
            }
            return false;
        },
        ...options
    });
};

// 主缓存实例
const redis = createRedisClient(1, "sixue:");

// WebSocket专用实例
const redisWebSocket = createRedisClient(10, "sixue:websocket:");

// 任务队列专用实例
const redisTask = createRedisClient(11, "sixue:task:", {
    // 任务队列需要更高的可靠性
    maxRetriesPerRequest: 5,
    connectTimeout: 15000
});

// 系统配置专用实例
const redisSystem = createRedisClient(
    process.env.REDIS_DB || 0,
    "sixue:system:",
    { enableAutoPipelining: true } // 启用自动管道化提高性能
);



export { redis, redisWebSocket, redisTask, redisSystem };