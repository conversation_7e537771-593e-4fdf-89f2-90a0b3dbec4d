// 通知相关的数据验证模式

// 创建通知的请求体模式
export const createNotificationSchema = {
  tags: ['notification'],
  body: {
    type: 'object',
    properties: {
      recipientType: { type: 'string', enum: ['individual', 'all'] },
      recipientIds: { type: 'array', items: { type: 'string' } },
      title: { type: 'string' },
      content: { type: 'string' }
    }
  }
};

// 获取通知列表的查询参数模式
export const getNotificationsQuerySchema = {
  tags: ['notification'],
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number' },
      pageSize: { type: 'number' },
      status: { type: 'string', default: 'all' }
    }
  }
};

// 获取通知详情的参数模式
export const getNotificationParamsSchema = {
  tags: ['notification'],
  params: {
    type: 'object',
    properties: {
      id: { type: 'string' }
    }
  }
};

// 批量处理已读的请求体模式
export const readNotificationsSchema = {
  tags: ['notification'],
  body: {
    type: 'object',
    properties: {
      ids: { type: 'array', items: { type: 'string' } }
    }
  }
};

// 批量删除的请求体模式
export const deleteNotificationsSchema = {
  tags: ['notification'],
  body: {
    type: 'object',
    properties: {
      ids: { type: 'array', items: { type: 'string' } }
    }
  }
}; 

// 批量标记为已读的请求体模式
export const readAllNotificationsSchema = {
  tags: ['notification'],
  summary: '批量标记为已读',
};
export default {
  createNotificationSchema,
  getNotificationsQuerySchema,
  getNotificationParamsSchema,
  readNotificationsSchema,
  readAllNotificationsSchema,
  deleteNotificationsSchema
};