/**
 * Redis Resilience Utilities
 * Provides resilient Redis operations with circuit breaker, retry, and fallback mechanisms
 */

// Circuit breaker state
const circuitState = {
    status: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
    failures: 0,
    lastFailure: 0,
    successfulProbes: 0
};

// Configuration
const config = {
    failureThreshold: 5, // Number of failures before opening circuit
    resetTimeout: 30000, // Time in ms before trying to close circuit again
    maxRetries: 3, // Maximum number of retries for operations
    retryDelay: 100, // Initial delay between retries in ms
    probeSuccessThreshold: 2 // Number of successful probes to close circuit
};

/**
 * Executes a Redis command with resilience patterns
 * @param {Object} redis - Redis client
 * @param {string} command - Redis command to execute
 * @param {Array} args - Arguments for the command
 * @param {Object} options - Options for resilience
 * @param {Function} fallback - Fallback function to execute if all retries fail
 * @returns {Promise<any>} - Result of the Redis command or fallback
 */
export async function resilientRedisCommand(redis, command, args = [], options = {}, fallback = null) {
    // Merge options with defaults
    const opts = {
        maxRetries: options.maxRetries || config.maxRetries,
        retryDelay: options.retryDelay || config.retryDelay,
        timeout: options.timeout || 5000, // Default timeout 5s
        logErrors: options.logErrors !== undefined ? options.logErrors : true
    };

    // Check circuit breaker state
    if (circuitState.status === 'OPEN') {
        // Check if it's time to try again
        const now = Date.now();
        if (now - circuitState.lastFailure > config.resetTimeout) {
            circuitState.status = 'HALF_OPEN';
            console.log(`[Redis Resilience] Circuit half-open, probing Redis with command: ${command}`);
        } else {
            // Circuit is open, use fallback if provided
            if (fallback) {
                return await fallback();
            }
            throw new Error(`[Redis Resilience] Circuit open, Redis command ${command} rejected`);
        }
    }

    // Try to execute the command with retries
    let lastError = null;
    let retryCount = 0;

    while (retryCount <= opts.maxRetries) {
        try {
            // Create a promise with timeout
            const result = await Promise.race([
                redis[command](...args),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error(`Redis command ${command} timed out after ${opts.timeout}ms`)), 
                    opts.timeout)
                )
            ]);

            // If we're in HALF_OPEN state and command succeeded, track success
            if (circuitState.status === 'HALF_OPEN') {
                circuitState.successfulProbes++;
                if (circuitState.successfulProbes >= config.probeSuccessThreshold) {
                    circuitState.status = 'CLOSED';
                    circuitState.failures = 0;
                    circuitState.successfulProbes = 0;
                    console.log('[Redis Resilience] Circuit closed after successful probes');
                }
            }

            return result;
        } catch (error) {
            lastError = error;
            
            // Log error if enabled
            if (opts.logErrors) {
                console.error(`[Redis Resilience] Redis command ${command} failed:`, error.message);
            }

            // Update circuit breaker state
            if (circuitState.status === 'CLOSED') {
                circuitState.failures++;
                if (circuitState.failures >= config.failureThreshold) {
                    circuitState.status = 'OPEN';
                    circuitState.lastFailure = Date.now();
                    console.log(`[Redis Resilience] Circuit opened after ${config.failureThreshold} failures`);
                }
            } else if (circuitState.status === 'HALF_OPEN') {
                // If a probe fails, open the circuit again
                circuitState.status = 'OPEN';
                circuitState.lastFailure = Date.now();
                circuitState.successfulProbes = 0;
                console.log('[Redis Resilience] Circuit reopened after failed probe');
            }

            // Check if we should retry
            if (retryCount < opts.maxRetries) {
                // Exponential backoff
                const delay = opts.retryDelay * Math.pow(2, retryCount);
                await new Promise(resolve => setTimeout(resolve, delay));
                retryCount++;
                console.log(`[Redis Resilience] Retrying Redis command ${command} (attempt ${retryCount}/${opts.maxRetries})`);
            } else {
                // All retries failed, use fallback if provided
                if (fallback) {
                    return await fallback();
                }
                throw lastError;
            }
        }
    }

    // This should not be reached, but just in case
    throw lastError;
}

/**
 * Resilient get operation
 * @param {Object} redis - Redis client
 * @param {string} key - Key to get
 * @param {Object} options - Options for resilience
 * @param {Function} fallback - Fallback function to execute if all retries fail
 * @returns {Promise<any>} - Result of the get operation or fallback
 */
export async function resilientGet(redis, key, options = {}, fallback = null) {
    return resilientRedisCommand(redis, 'get', [key], options, fallback);
}

/**
 * Resilient set operation
 * @param {Object} redis - Redis client
 * @param {string} key - Key to set
 * @param {string} value - Value to set
 * @param {string} mode - Set mode (e.g., 'EX', 'NX')
 * @param {number} expiry - Expiry time if applicable
 * @param {Object} options - Options for resilience
 * @param {Function} fallback - Fallback function to execute if all retries fail
 * @returns {Promise<any>} - Result of the set operation or fallback
 */
export async function resilientSet(redis, key, value, mode = null, expiry = null, options = {}, fallback = null) {
    const args = [key, value];
    if (mode) {
        args.push(mode);
        if (expiry !== null) {
            args.push(expiry);
        }
    }
    return resilientRedisCommand(redis, 'set', args, options, fallback);
}

/**
 * Resilient del operation
 * @param {Object} redis - Redis client
 * @param {string|Array} keys - Key(s) to delete
 * @param {Object} options - Options for resilience
 * @param {Function} fallback - Fallback function to execute if all retries fail
 * @returns {Promise<any>} - Result of the del operation or fallback
 */
export async function resilientDel(redis, keys, options = {}, fallback = null) {
    const args = Array.isArray(keys) ? keys : [keys];
    return resilientRedisCommand(redis, 'del', args, options, fallback);
}

/**
 * Resilient multi/exec operation
 * @param {Object} redis - Redis client
 * @param {Function} commands - Function that receives a multi object and adds commands
 * @param {Object} options - Options for resilience
 * @param {Function} fallback - Fallback function to execute if all retries fail
 * @returns {Promise<any>} - Result of the multi/exec operation or fallback
 */
export async function resilientMulti(redis, commands, options = {}, fallback = null) {
    return resilientRedisCommand(
        redis, 
        'multi', 
        [], 
        options, 
        fallback
    ).then(multi => {
        commands(multi);
        return resilientRedisCommand(
            multi, 
            'exec', 
            [], 
            options, 
            fallback ? () => fallback() : null
        );
    });
}

/**
 * Get circuit breaker status
 * @returns {Object} Current circuit breaker state
 */
export function getCircuitStatus() {
    return { ...circuitState };
}

/**
 * Reset circuit breaker
 */
export function resetCircuit() {
    circuitState.status = 'CLOSED';
    circuitState.failures = 0;
    circuitState.lastFailure = 0;
    circuitState.successfulProbes = 0;
    console.log('[Redis Resilience] Circuit manually reset to CLOSED');
}

export default {
    resilientRedisCommand,
    resilientGet,
    resilientSet,
    resilientDel,
    resilientMulti,
    getCircuitStatus,
    resetCircuit
};
