import menuController from '../controllers/menuController-new.js';
import menuSchema from '../schemas/menuSchema.js';

/**
 * Menu Routes
 * Defines API endpoints for menu-related operations
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Route options
 */
export default async function (fastify, options) {
    // Get user menus
    fastify.get('/menu-new/user', {
        schema: menuSchema.getUserMenusSchema,
        onRequest: [fastify.auth.authenticate],
        handler: menuController.getUserMenus
    });
    
    // Get all menus
    fastify.get('/menu-new/all', {
        schema: menuSchema.getAllMenusSchema,
        onRequest: [fastify.auth.authenticate],
        handler: menuController.getAllMenus
    });
    
    // Get role menus
    fastify.get('/menu-new/role/:roleId', {
        schema: menuSchema.getRoleMenusSchema,
        onRequest: [fastify.auth.authenticate],
        handler: menuController.getRoleMenus
    });
    
    // Update role menus
    fastify.put('/menu-new/role/:roleId', {
        schema: menuSchema.updateRoleMenusSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:menu:update')
        ],
        handler: menuController.updateRoleMenus
    });
    
    // Create menu
    fastify.post('/menu-new', {
        schema: menuSchema.createMenuSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:menu:create')
        ],
        handler: menuController.createMenu
    });
    
    // Update menu
    fastify.put('/menu-new/:menuId', {
        schema: menuSchema.updateMenuSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:menu:update')
        ],
        handler: menuController.updateMenu
    });
    
    // Delete menu
    fastify.delete('/menu-new/:menuId', {
        schema: menuSchema.deleteMenuSchema,
        onRequest: [
            fastify.auth.authenticate,
            fastify.auth.requirePermission('system:menu:delete')
        ],
        handler: menuController.deleteMenu
    });
}
