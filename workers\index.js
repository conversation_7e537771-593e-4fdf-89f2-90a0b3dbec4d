/**
 * Workers index file
 * Exports all BullMQ workers for easy importing
 */

// Import queues from the queues directory
import {
  imageQueue,
  textQueue,
  speechQueue,
  visionQueue
} from '../queues/index.js';

// Re-export the queues
export {
  imageQueue,
  textQueue,
  speechQueue,
  visionQueue
};

/**
 * Initialize all workers
 * This function can be called to start all workers at once
 */
export const initializeWorkers = () => {
  console.log('Initializing BullMQ workers...');

  // Log the status of each queue
  console.log('Image generation queue initialized');
  console.log('Text generation queue initialized');
  console.log('Speech processing queue initialized');
  console.log('Vision processing queue initialized');

  return {
    imageQueue,
    textQueue,
    speechQueue,
    visionQueue
  };
};

// Export default as the initialize function for easy importing
export default initializeWorkers;
