import fp from "fastify-plugin";
import auth from "@fastify/auth";
import { createError } from "@fastify/error";

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);

async function authPlugin(fastify, opts) {
    // 注册 @fastify/auth 插件
    await fastify.register(auth);

    // 验证 token 的函数
    async function verifyToken(request, reply) {
        try {
            const token = request.headers.authorization?.replace('Bearer ', '');

            if (!token) {
                throw new AUTH_ERROR('未提供令牌');
            }

            const decoded = await fastify.jwt.verify(token);
            // 获取用户信息
            const user = await fastify.prisma.user.findUnique({
                where: { id: decoded.id },
            });

            if (!user) {
                throw new AUTH_ERROR('用户不存在');
            }

            // 将用户信息添加到请求对象中
            request.user = user;
        } catch (error) {
            throw new AUTH_ERROR('无效的令牌');
        }
    }

    // 将验证函数装饰到 fastify 实例上
    // fastify.decorate('verifyToken', verifyToken);
}

export default fp(authPlugin, {
    name: 'auth-plugin',
    dependencies: ['@fastify/jwt', 'prisma-connector']
}); 