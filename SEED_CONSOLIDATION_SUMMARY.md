# Prisma 数据种子脚本整理总结

## 概述
成功将分散在多个文件中的 Prisma 数据种子脚本整理为一个统一的、结构化的脚本文件。

## 整理前的问题
项目中存在多个重复和分散的种子脚本：

### 主要种子文件
- `scripts/seed.js` - 主要种子脚本
- `scripts/seed copy.js` - 重复的备份文件
- `prisma/seed.js` - 不同实现的种子脚本
- `prisma/seed.ts` - TypeScript 版本
- `prisma/seed.sql` - SQL 版本

### 专门化种子文件
- `prisma/permission-seed.js` - 仅权限数据
- `prisma/menu-seed.js` - 仅菜单数据
- `prisma/role-permission-seed.js` - 角色权限映射
- `prisma/link-permissions-menus.js` - 权限菜单关联

### package.json 中的冗余脚本
```json
{
  "prisma:seed": "node prisma/seed.js",
  "seed:permissions": "node prisma/permission-seed.js",
  "seed:menus": "node prisma/menu-seed.js",
  "seed:link": "node prisma/link-permissions-menus.js",
  "seed:all": "npm run seed:permissions && npm run seed:menus && npm run seed:link"
}
```

## 整理后的结构

### 统一的种子脚本 (`scripts/seed.js`)
重新组织为模块化的函数结构：

1. **cleanupExistingData()** - 清理现有数据
2. **createPermissions()** - 创建所有权限
3. **createRoles()** - 创建角色
4. **assignRolePermissions()** - 分配角色权限
5. **createUsersAndInstitutions()** - 创建用户和机构
6. **createMenus()** - 创建菜单系统
7. **main()** - 主执行函数

### 权限系统
整合了完整的权限体系：
- **教务中心权限**: 学员、班级、课表、课程、套餐管理
- **招生中心权限**: 跟进管理、意向学员、公海池
- **财务中心权限**: 销售记录、收银记账、工资管理
- **数据中心权限**: 教务数据、销售数据统计
- **机构中心权限**: 机构信息、员工管理、教室管理
- **系统管理权限**: 用户、角色、菜单、机构管理
- **通用权限**: 仪表盘、通知、AI工具访问

### 角色体系
定义了三个核心角色：
- **超级管理员** (`superadmin`): 拥有所有权限
- **机构管理员** (`orgadmin`): 机构级别管理权限
- **教师** (`teacher`): 教学相关权限

### 菜单系统
创建了完整的菜单层次结构：
- 主菜单：仪表盘、教务中心、招生中心、财务中心、数据中心、机构中心、系统管理、通知管理、AI工具
- 子菜单：每个主菜单下的具体功能模块

## 删除的冗余文件
- `scripts/seed copy.js`
- `prisma/seed.js`
- `prisma/seed.ts`
- `prisma/seed.sql`
- `prisma/permission-seed.js`
- `prisma/menu-seed.js`
- `prisma/role-permission-seed.js`
- `prisma/link-permissions-menus.js`

## 简化的 package.json 脚本
```json
{
  "seed": "node scripts/seed.js"
}
```

## 使用方法
```bash
# 运行种子脚本
npm run seed
```

## 创建的默认用户
脚本会创建以下测试用户：
- **超级管理员**: `superadmin` / `123456`
- **机构管理员**: `orgadmin` / `123456`
- **教师用户**: `teacher` / `123456`

## 优势
1. **统一管理**: 所有种子数据在一个文件中管理
2. **模块化**: 功能分离，易于维护和扩展
3. **完整性**: 包含权限、角色、用户、机构、菜单的完整数据
4. **可靠性**: 使用 upsert 和事务确保数据一致性
5. **清晰的日志**: 详细的执行步骤和结果反馈

## 技术特点
- 使用 bcrypt 加密用户密码
- 支持数据清理和重新创建
- 自动处理关联关系
- 错误处理和回滚机制
- 彩色日志输出，便于调试
