/**
 * Students Schema
 * Defines validation schemas for student-related endpoints
 */
const studentsSchema = {
    /**
     * Schema for getting students list
     */
    getStudentsListSchema: {
        tags: ['students'],
        summary: '获取学员列表',
        description: '获取学员列表，支持分页和搜索',
        querystring: {
            type: 'object',
            properties: {
                page: {
                    type: 'number',
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: {
                    type: 'number',
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: {
                    type: 'string',
                    description: '搜索关键词，支持姓名和电话号码搜索'
                },
                follower: {
                    type: 'string',
                    description: '跟进人ID'
                },
                intention: {
                    type: 'string',
                    description: '意向类型'
                },
                type: {
                    type: 'string',
                    default: 'formal',
                    description: '学生类型，如formal（正式学员）、intent（意向学员）等'
                },
                intentLevel: {
                    type: 'string',
                    description: '意向等级'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        phone: { type: 'string' },
                                        gender: { type: 'string' },
                                        birthday: { type: 'number' },
                                        source: { type: 'string' },
                                        intentionLevel: { type: 'string' },
                                        type: { type: 'string' },
                                        status: { type: 'string' },
                                        followerName: { type: 'string' },
                                        remarks: { type: 'string' },
                                        address: { type: 'string' },
                                        followUpDate: { type: 'number' },
                                        createdAt: { type: 'string' }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting students select list
     */
    getStudentsSelectListSchema: {
        tags: ['students'],
        summary: '获取学员选择列表',
        description: '获取学员选择列表，用于下拉选择框',
        querystring: {
            type: 'object',
            properties: {
                page: {
                    type: 'number',
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: {
                    type: 'number',
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: {
                    type: 'string',
                    description: '搜索关键词，支持姓名和电话号码搜索'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        phone: { type: 'string' }
                                    }
                                }
                            },
                            total: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting student classes
     */
    getStudentClassesSchema: {
        tags: ['students'],
        summary: '获取学员上课记录',
        description: '获取学员上课记录，支持分页和筛选',
        querystring: {
            type: 'object',
            properties: {
                page: {
                    type: 'number',
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: {
                    type: 'number',
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: {
                    type: 'string',
                    description: '搜索关键词'
                },
                studentId: {
                    type: 'string',
                    description: '学员ID'
                },
                status: {
                    type: 'string',
                    default: 'all',
                    description: '状态，如all（全部）、attended（已上课）、absent（缺勤）等'
                },
                startDate: {
                    type: 'string',
                    description: '开始日期'
                },
                endDate: {
                    type: 'string',
                    description: '结束日期'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        classId: { type: 'string' },
                                        className: { type: 'string' },
                                        courseName: { type: 'string' },
                                        teacherName: { type: 'string' },
                                        startTime: { type: 'number' },
                                        endTime: { type: 'number' },
                                        status: { type: 'string' },
                                        remarks: { type: 'string' }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    }
                }
            }
        }
    },

    /**
     * Schema for getting student by ID
     */
    getStudentByIdSchema: {
        tags: ['students'],
        summary: '获取学员详情',
        description: '根据学员ID获取学员详细信息',
        params: {
            type: 'object',
            required: ['studentId'],
            properties: {
                studentId: {
                    type: 'string',
                    description: '学员ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            phone: { type: 'string' },
                            gender: { type: 'string' },
                            birthday: { type: 'number' },
                            balance: { type: 'number' },
                            points: { type: 'number' },
                            followUpPerson: { type: 'string' },
                            followUpDate: { type: 'string' },
                            source: { type: 'string' },
                            referrer: { type: 'string' },
                            address: { type: 'string' },
                            idCard: { type: 'string' },
                            school: { type: 'string' },
                            intentionLevel: { type: 'string' },
                            parentName: { type: 'string' },
                            status: { type: 'string' },
                            remark: { type: 'string' },
                            type: { type: 'string' },
                            // createdAt: { type: 'string' },
                            // updatedAt: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for creating student
     */
    createStudentSchema: {
        tags: ['students'],
        summary: '创建学员',
        description: '创建新学员',
        body: {
            type: 'object',
            required: ['name', 'phone'],
            properties: {
                name: {
                    type: 'string',
                    description: '学员姓名'
                },
                gender: {
                    type: 'string',
                    enum: ['male', 'female', 'other'],
                    description: '性别'
                },
                phone: {
                    type: 'string',
                    description: '电话号码'
                },
                age: {
                    type: 'number',
                    description: '年龄'
                },
                birthday: {
                    type: 'string',
                    format: 'date-time',
                    description: '生日'
                },
                email: {
                    type: 'string',
                    format: 'email',
                    description: '电子邮箱'
                },
                balance: {
                    type: 'number',
                    default: 0,
                    description: '余额'
                },
                points: {
                    type: 'number',
                    default: 0,
                    description: '积分'
                },
                followUpPerson: {
                    type: 'string',
                    description: '跟进人'
                },
                followUpDate: {
                    type: 'string',
                    format: 'date-time',
                    description: '跟进日期'
                },
                source: {
                    type: 'string',
                    description: '来源'
                },
                referrer: {
                    type: 'string',
                    description: '推荐人'
                },
                address: {
                    type: 'string',
                    description: '地址'
                },
                idCard: {
                    type: 'string',
                    description: '身份证号'
                },
                school: {
                    type: 'string',
                    description: '学校'
                },
                intentionLevel: {
                    type: 'string',
                    description: '意向等级'
                },
                parentName: {
                    type: 'string',
                    description: '家长姓名'
                },
                status: {
                    type: 'string',
                    enum: ['active', 'inactive', 'deleted'],
                    default: 'active',
                    description: '状态'
                },
                remark: {
                    type: 'string',
                    description: '备注'
                },
                type: {
                    type: 'string',
                    enum: ['formal', 'intent', 'public', 'graduated'],
                    default: 'formal',
                    description: '类型'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            phone: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for updating student
     */
    updateStudentSchema: {
        tags: ['students'],
        summary: '更新学员',
        description: '更新学员信息',
        params: {
            type: 'object',
            required: ['studentId'],
            properties: {
                studentId: {
                    type: 'string',
                    description: '学员ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                name: { type: 'string' },
                gender: {
                    type: 'string',
                    enum: ['male', 'female', 'other']
                },
                phone: { type: 'string' },
                age: { type: 'number' },
                birthday: {
                    type: 'string',
                    format: 'date-time'
                },
                email: {
                    type: 'string',
                    format: 'email'
                },
                balance: { type: 'number' },
                points: { type: 'number' },
                followUpPerson: { type: 'string' },
                followUpDate: {
                    type: 'string',
                    format: 'date-time'
                },
                source: { type: 'string' },
                referrer: { type: 'string' },
                address: { type: 'string' },
                idCard: { type: 'string' },
                school: { type: 'string' },
                intentionLevel: { type: 'string' },
                parentName: { type: 'string' },
                status: {
                    type: 'string',
                    enum: ['active', 'inactive', 'deleted']
                },
                remark: { type: 'string' },
                type: {
                    type: 'string',
                    enum: ['formal', 'intent', 'public', 'graduated']
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for deleting student
     */
    deleteStudentSchema: {
        tags: ['students'],
        summary: '删除学员',
        description: '批量删除学员',
        body: {
            type: 'object',
            required: ['studentIds'],
            properties: {
                studentIds: {
                    type: 'array',
                    items: { type: 'string' },
                    description: '学员ID数组'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    }
,

    /**
     * Schema for creating student follow-up
     */
    createStudentFollowUpSchema: {
        tags: ['students'],
        summary: '创建学员跟进记录',
        description: '为学员创建跟进记录',
        body: {
            type: 'object',
            required: ['studentId'],
            properties: {
                studentId: {
                    type: 'string',
                    description: '学员ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },
    /**
     * Schema for add student
     */
    studentAddSchema: {
        tags: ['students'],
        summary: '添加学员',
        description: '添加新学员',
        body: {
            type: 'object',
            required: ['name', 'phone'],
            properties: {
                name: {
                    type: 'string',
                    description: '学员姓名'
                },
                gender: {
                    type: 'string',
                    enum: ['male', 'female', 'secret'],
                    description: '性别'
                },
                phone: {
                    type: 'string',
                    description: '电话号码'
                },
                birthday: {
                    type: 'string',
                    description: '生日'
                },

                source: {
                    type: 'string',
                    description: '来源'
                },
                sourceDesc: {
                    type: 'string',
                    description: '来源描述'
                },
                referrer: {
                    type: 'string',
                    description: '推荐人'
                },
                follower: {
                    type: 'string',
                    description: '跟进人'
                },
                address: {
                    type: 'string',
                    description: '地址'
                },
                idCard: {
                    type: 'string',
                    description: '身份证号'
                },
                intention: {
                    type: 'string',
                    description: '意向等级'
                },
                remark: {
                    type: 'string',
                    description: '备注'
                },
                type: {
                    type: 'string',
                    enum: ['formal', 'intent', 'public', 'graduated'],
                    default: 'formal',
                    description: '类型'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for registering student
     */
    registerStudentSchema: {
        tags: ['students'],
        summary: '注册学员',
        description: '公开接口，用于学员自主注册',
        params: {
            type: 'object',
            required: ['institutionId'],
            properties: {
                institutionId: {
                    type: 'string',
                    description: '机构ID'
                }
            }
        },
        body: {
            type: 'object',
            required: ['name', 'phone', 'gender'],
            properties: {
                name: {
                    type: 'string',
                    description: '学员姓名'
                },
                gender: {
                    type: 'string',
                    description: '性别'
                },
                phone: {
                    type: 'string',
                    description: '电话号码'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            phone: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting student products
     */
    getStudentProductsSchema: {
        tags: ['students'],
        summary: '获取学员套餐列表',
        description: '获取学员套餐列表，支持分页和筛选',
        querystring: {
            type: 'object',
            properties: {
                page: {
                    type: 'number',
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: {
                    type: 'number',
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: {
                    type: 'string',
                    description: '搜索关键词'
                },
                remainingTimesMin: {
                    type: 'number',
                    description: '最小剩余次数'
                },
                remainingTimesMax: {
                    type: 'number',
                    description: '最大剩余次数'
                },
                remainingDaysMin: {
                    type: 'number',
                    description: '最小剩余天数'
                },
                remainingDaysMax: {
                    type: 'number',
                    description: '最大剩余天数'
                },
                status: {
                    type: 'string',
                    description: '状态'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        startDate: { type: 'number' },
                                        endDate: { type: 'number' },
                                        totalSessionCount: { type: 'number' },
                                        remainingSessionCount: { type: 'number' },
                                        sessionUnitPrice: { type: 'number' },
                                        enrollmentStatus: { type: 'string' },
                                        remainingBalance: { type: 'number' },
                                        product: {
                                            type: 'object',
                                            properties: {
                                                id: { type: 'string' },
                                                name: { type: 'string' },
                                                packageType: { type: 'string' }
                                            }
                                        },
                                        student: {
                                            type: 'object',
                                            properties: {
                                                id: { type: 'string' },
                                                name: { type: 'string' },
                                                phone: { type: 'string' }
                                            }
                                        }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for updating student product
     */
    updateStudentProductSchema: {
        tags: ['students'],
        summary: '更新学员套餐',
        description: '更新学员套餐内容',
        params: {
            type: 'object',
            required: ['studentProductId'],
            properties: {
                studentProductId: {
                    type: 'string',
                    description: '学员套餐ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                remainingCount: {
                    type: 'number',
                    description: '剩余次数'
                },
                remarks: {
                    type: 'string',
                    description: '备注'
                },
                remainingDays: {
                    type: 'number',
                    description: '剩余天数'
                },
                status: {
                    type: 'string',
                    description: '状态'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for creating student product
     */
    createStudentProductSchema: {
        tags: ['students'],
        summary: '创建学员套餐',
        description: '为学员创建套餐',
        params: {
            type: 'object',
            required: ['studentId'],
            properties: {
                studentId: {
                    type: 'string',
                    description: '学员ID'
                }
            }
        },
        body: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: {
                    type: 'string',
                    description: '产品ID'
                },
                amount: {
                    type: 'number',
                    description: '金额'
                },
                bonusLessons: {
                    type: 'number',
                    description: '赠送课时'
                },
                dateTime: {
                    type: 'number',
                    description: '支付时间'
                },
                remarks: {
                    type: 'string',
                    description: '备注'
                },
                payment: {
                    type: 'string',
                    description: '支付方式'
                },
                salesRep: {
                    type: 'string',
                    description: '销售代表'
                },
                prepaidAmount: {
                    type: 'number',
                    description: '预付金额'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting student product adjustments
     */
    getStudentProductAdjustmentsSchema: {
        tags: ['students'],
        summary: '获取学员套餐调整记录',
        description: '获取学员套餐调整记录',
        params: {
            type: 'object',
            required: ['studentId'],
            properties: {
                studentId: {
                    type: 'string',
                    description: '学员ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                studentId: { type: 'string' },
                                studentProductId: { type: 'string' },
                                beforeCount: { type: 'number' },
                                afterCount: { type: 'number' },
                                type: { type: 'string' },
                                operatorId: { type: 'string' },
                                operatorTime: { type: 'number' },
                                remarks: { type: 'string' }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },

    /**
     * Schema for getting intent students
     */
    getIntentStudentsSchema: {
        tags: ['students'],
        summary: '获取意向学员列表',
        description: '获取意向学员列表，支持分页和搜索',
        querystring: {
            type: 'object',
            properties: {
                page: {
                    type: 'number',
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: {
                    type: 'number',
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: {
                    type: 'string',
                    description: '搜索关键词'
                },
                status: {
                    type: 'string',
                    description: '状态'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        phone: { type: 'string' },
                                        gender: { type: 'string' },
                                        birthday: { type: 'number' },
                                        source: { type: 'string' },
                                        sourceDesc: { type: 'string' },
                                        followUpDate: { type: 'number' },
                                        intentLevel: { type: 'string' },
                                        type: { type: 'string' },
                                        follower: {
                                            type: 'object',
                                            properties: {
                                                id: { type: 'string' },
                                                name: { type: 'string' }
                                            }
                                        }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    // 获取意向学员列表的schema
    getIntentStudentsSchema: {
        tags: ['students'],
        summary: '获取意向学员列表',
        querystring: {
        type: 'object',
        properties: {
            search: { type: 'string' },
            page: { type: 'number', default: 1 },
            pageSize: { type: 'number', default: 10 }
        }
    }
},

// 获取考勤记录的schema
getAttendanceRecordsSchema:{
    tags: ['students'],
    summary: '获取学员考勤记录',
    querystring: {
        type: 'object',
        properties: {
            search: { type: 'string' },
            page: { type: 'number', default: 1 },
            pageSize: { type: 'number', default: 10 },
            status: {
                type: 'string',
                default: 'attendance',
                enum: ['attendance', 'leave', 'absent', 'unattended']
            },
            endTime: { type: 'number' },
            startTime: { type: 'number' },
            teacherId: { type: 'string' }
        }
    }
} 
};

export default studentsSchema;
