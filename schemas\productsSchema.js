/**
 * Products Schema
 * Defines validation schemas for product-related endpoints
 */
const productsSchema = {
    /**
     * Schema for getting products list
     */
    getProductsListSchema: {
        tags: ['products'],
        summary: '获取产品列表',
        description: '获取产品列表，支持分页和搜索',
        querystring: {
            type: 'object',
            properties: {
                page: { 
                    type: 'number', 
                    default: 1,
                    description: '页码，从1开始'
                },
                pageSize: { 
                    type: 'number', 
                    default: 10,
                    description: '每页数量，默认10'
                },
                search: { 
                    type: 'string',
                    description: '搜索关键词，支持产品名称搜索'
                },
                type: { 
                    type: 'string',
                    description: '产品类型，如课时包、会员卡等'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            list: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        price: { type: 'number' },
                                        leaveCount: { type: 'number' },
                                        packageType: { type: 'string' },
                                        usageLimit: { type: 'number' },
                                        validTimeRange: { type: 'string' },
                                        timeLimitedUsage: { type: 'number' },
                                        timeLimitType: { type: 'string' },
                                        remarks: { type: 'string' },
                                        status: { type: 'string' },
                                        createdAt: { type: 'number' }
                                    }
                                }
                            },
                            total: { type: 'number' },
                            page: { type: 'number' },
                            pageSize: { type: 'number' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting all active products
     */
    getAllActiveProductsSchema: {
        tags: ['products'],
        summary: '获取全部可用产品',
        description: '获取全部可用产品，不分页',
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                name: { type: 'string' },
                                price: { type: 'number' },
                                leaveCount: { type: 'number' },
                                packageType: { type: 'string' },
                                usageLimit: { type: 'number' },
                                validTimeRange: { type: 'string' },
                                timeLimitedUsage: { type: 'number' },
                                timeLimitType: { type: 'string' }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for creating product
     */
    createProductSchema: {
        tags: ['products'],
        summary: '创建产品',
        description: '创建新产品',
        body: {
            type: 'object',
            required: ['name', 'price'],
            properties: {
                name: { 
                    type: 'string',
                    description: '产品名称'
                },
                price: { 
                    type: 'number',
                    description: '产品价格'
                },
                icon: { 
                    type: 'string',
                    description: '产品图标URL'
                },
                cover: { 
                    type: 'string',
                    description: '产品封面图片URL'
                },
                leaveCount: { 
                    type: 'number',
                    description: '课时数量'
                },
                packageType: { 
                    type: 'string',
                    description: '课程包类型，如课时包、会员卡等'
                },
                usageLimit: { 
                    type: 'number',
                    description: '使用限制'
                },
                timeLimitedUsage: { 
                    type: 'number',
                    description: '限时消费时长'
                },
                timeLimitType: { 
                    type: 'string',
                    description: '限时消费类型，如天、周、月等'
                },
                validTimeRange: { 
                    type: 'string',
                    description: '有效时间范围，如购买日算起、消费日算起等'
                },
                targetAudience: { 
                    type: 'string',
                    description: '适用人群'
                },
                isShow: { 
                    type: 'boolean',
                    description: '是否显示'
                },
                remarks: { 
                    type: 'string',
                    description: '备注'
                },
                status: { 
                    type: 'string',
                    description: '产品状态，如active、inactive等'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            price: { type: 'number' },
                            packageType: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for updating product
     */
    updateProductSchema: {
        tags: ['products'],
        summary: '更新产品',
        description: '更新产品信息',
        params: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        body: {
            type: 'object',
            properties: {
                name: { type: 'string' },
                price: { type: 'number' },
                icon: { type: 'string' },
                cover: { type: 'string' },
                leaveCount: { type: 'number' },
                packageType: { type: 'string' },
                usageLimit: { type: 'number' },
                timeLimitedUsage: { type: 'number' },
                timeLimitType: { type: 'string' },
                validTimeRange: { type: 'string' },
                targetAudience: { type: 'string' },
                isShow: { type: 'boolean' },
                remarks: { type: 'string' },
                status: { type: 'string' }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            price: { type: 'number' },
                            packageType: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for deleting product
     */
    deleteProductSchema: {
        tags: ['products'],
        summary: '删除产品',
        description: '删除产品',
        params: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting product by ID
     */
    getProductByIdSchema: {
        tags: ['products'],
        summary: '获取产品详情',
        description: '根据ID获取产品详情',
        params: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            price: { type: 'number' },
                            icon: { type: 'string' },
                            cover: { type: 'string' },
                            leaveCount: { type: 'number' },
                            packageType: { type: 'string' },
                            usageLimit: { type: 'number' },
                            timeLimitedUsage: { type: 'number' },
                            timeLimitType: { type: 'string' },
                            validTimeRange: { type: 'string' },
                            targetAudience: { type: 'string' },
                            isShow: { type: 'boolean' },
                            remarks: { type: 'string' },
                            status: { type: 'string' },
                            createdAt: { type: 'number' },
                            courses: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        type: { type: 'string' },
                                        duration: { type: 'number' },
                                        status: { type: 'string' }
                                    }
                                }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for getting product courses
     */
    getProductCoursesSchema: {
        tags: ['products'],
        summary: '获取套餐绑定的课程',
        description: '获取套餐绑定的课程列表',
        params: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    data: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                courseId: { type: 'string' },
                                productId: { type: 'string' },
                                course: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        name: { type: 'string' },
                                        type: { type: 'string' },
                                        duration: { type: 'number' },
                                        status: { type: 'string' }
                                    }
                                }
                            }
                        }
                    },
                    message: { type: 'string' }
                }
            }
        }
    },
    
    /**
     * Schema for updating product courses
     */
    updateProductCoursesSchema: {
        tags: ['products'],
        summary: '套餐绑定课程',
        description: '更新套餐绑定的课程列表',
        params: {
            type: 'object',
            required: ['productId'],
            properties: {
                productId: { 
                    type: 'string',
                    description: '产品ID'
                }
            }
        },
        body: {
            type: 'object',
            required: ['courseIds'],
            properties: {
                courseIds: { 
                    type: 'array',
                    items: { type: 'string' },
                    description: '课程ID数组'
                }
            }
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    code: { type: 'number' },
                    message: { type: 'string' }
                }
            }
        }
    }
};

export default productsSchema;
