/**
 * 获取指定日期所在周的开始和结束时间
 * @param {string} dateString - 日期字符串
 * @returns {Object} 包含开始和结束时间的对象
 */
export default function getWeekStartAndEnd(dateString) {
    // 将输入日期字符串转换为 Date 对象
    const date = new Date(dateString);
  
    // 获取当前日期是星期几（0 是周日，1 是周一，...，6 是周六）
    const dayOfWeek = date.getDay();
  
    // 计算本周的开始时间（周一）
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1)); // 如果当前是周日，减去 6 天；否则减去 (dayOfWeek - 1) 天
    startOfWeek.setHours(0, 0, 0, 0); // 将时间设置为当天的 00:00:00.000
    startOfWeek.setMinutes(0);

    // 计算本周的结束时间（周日）
    const endOfWeek = new Date(date);
    endOfWeek.setDate(date.getDate() + (7 - dayOfWeek)); // 加上剩余的天数
    endOfWeek.setHours(23, 59, 59, 999); // 将时间设置为当天的 23:59:59.999
    endOfWeek.setMinutes(0);

    return {
      startOfWeek,
      endOfWeek,
    };
  }
