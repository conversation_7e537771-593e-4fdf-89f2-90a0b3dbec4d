/**
 * Vision processing worker for BullMQ
 * Handles image analysis and captioning tasks
 */

import { Worker } from "bullmq";
import { redisTask } from "../config/redis.js";
import { analyzeImage, captionImage } from "../services/visionService.js";
import {
  defaultWorkerOptions,
  markTaskCompleted,
  markTaskFailed,
  setupProgressReporting
} from "./workerConfig.js";
import visionQueue from "../queues/visionQueue.js";

// Create the worker
const worker = new Worker(
  'vision-processing',
  async (job) => {
    const { taskId, type } = job.data;
    console.log(`Starting vision processing job ${job.id}, taskId: ${taskId}, type: ${type}`);

    const startTime = Date.now();

    // Setup progress reporting
    const { progressInterval, updateProgress } = setupProgressReporting(taskId);

    try {
      let result;

      // Process different types of vision processing tasks
      if (type === 'image-analysis') {
        const { imageUrl, features } = job.data;

        // Update initial progress
        await updateProgress(10);

        // Analyze the image
        result = await analyzeImage(imageUrl, features);
      } else if (type === 'image-captioning') {
        const { imageUrl, maxLength } = job.data;

        // Update initial progress
        await updateProgress(10);

        // Generate caption for the image
        result = await captionImage(imageUrl, maxLength);
      } else {
        throw new Error(`Unsupported vision task type: ${type}`);
      }

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Mark task as completed
      await markTaskCompleted(taskId, result, type, processingTime);

      console.log(`Vision processing job ${job.id} completed, processing time: ${processingTime}ms`);
      return result;
    } catch (error) {
      console.error(`Vision processing job ${job.id} failed:`, error);

      // Clear progress reporting interval
      clearInterval(progressInterval);

      // Mark task as failed
      await markTaskFailed(taskId, error, type, Date.now() - startTime);

      // Rethrow the error for BullMQ to handle retries
      throw error;
    }
  },
  defaultWorkerOptions
);

// Add error handling for the worker
worker.on('error', (error) => {
  console.error('Vision processing worker error:', error);
});

worker.on('failed', (job, error) => {
  console.error(`Vision processing worker job ${job?.id} failed:`, error);
});

worker.on('completed', (job) => {
  console.log(`Vision processing worker completed job ${job.id}`);
});

export default visionQueue;
