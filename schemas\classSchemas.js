// 临时计划 Schema
export const tempScheduleSchema = {
    tags: ['classes'],
    summary: '快捷创建临时计划',
    body: {
        type: 'object',
        properties: {
            name: { type: 'string' },
            teacherId: { type: 'string' },
            courseId: { type: 'string' },
            classRoomId: { type: 'string' },
            maxStudentCount: { type: 'number' },
            weekdays: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        day: { type: 'number', default: 1 },
                        startTime: { type: 'string' },
                        endTime: { type: 'string' }
                    }
                }
            },
            reservation: {
                type: 'object',
                properties: {
                    enabled: { type: 'boolean', default: false },
                    appointmentStartTime: { type: 'number', default: 0 },
                    appointmentEndTime: { type: 'number', default: 0 }
                }
            },
            attendance: {
                type: 'object',
                properties: {
                    studentScan: { type: 'boolean', default: false },
                    autoSystem: { type: 'boolean', default: false }
                }
            },
            leave: {
                type: 'object',
                properties: {
                    enabled: { type: 'boolean', default: false },
                    leaveDeadline: { type: 'number', default: 0 }
                }
            }
        }
    }
};

// 获取计划表 Schema
export const getAllSchedulesSchema = {
    tags: ['classes'],
    summary: '获取计划表',
    querystring: {
        type: 'object',
        properties: {
            startDate: { type: 'number' },
            endDate: { type: 'number' },
            teacher: { type: 'string', default: '' }
        }
    },
    response: {
        200: {
            type: 'object',
            properties: {
                code: { type: 'number' },
                data: { 
                    type: 'array', 
                    items: { 
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            startDate: { type: 'number' },
                            weekDay: { type: 'number' },
                            startTime: { type: 'string' },
                            endTime: { type: 'string' },
                            maxStudentCount: { type: 'number' },
                            subject: { type: 'string' },
                            teacherName: { type: 'string' },
                            classesType: { type: 'string' },
                            className: { type: 'string' },
                            courseName: { type: 'string' },
                            studentCount: { type: 'number' }
                        }
                    }   
                },
                message: { type: 'string' }
            }
        }
    }
};

// 获取班级列表 Schema
export const getClassesSchema = {
    tags: ['classes'],
    summary: '获取班级列表',
    querystring: {
        type: 'object',
        properties: {
            page: { type: 'number', default: 1 },
            pageSize: { type: 'number', default: 10 },
            teacherId: { type: 'string' },
            name: { type: 'string' }
        }
    }
};

// 创建班级 Schema
export const createClassSchema = {
    tags: ['classes'],
    summary: '创建班级',
    body: {
        type: 'object',
        required: ['name'],
        properties: {
            name: { type: 'string' },
            teacherId: { type: 'string' },
            classRoomId: { type: 'string' },
            maxStudentCount: { type: 'number', default: 20 },
            times: { type: 'number' },
            recurrenceType: { type: 'string', default: 'weekly' },
            daily: {
                type: 'object',
                properties: {
                    startTime: { type: 'string' },
                    endTime: { type: 'string' }
                }
            },
            startDate: { type: 'number' },
            endDate: { type: 'number' },
            endType: { type: 'string', default: 'number_of_times' },
            weekdays: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        day: { type: 'number', default: 1 },
                        startTime: { type: 'string' },
                        endTime: { type: 'string' }
                    }
                }
            },
            courseId: { type: 'string' },
            reservation: {
                type: 'object',
                properties: {
                    enabled: { type: 'boolean', default: false },
                    appointmentStartTime: { type: 'number', default: 0 },
                    appointmentEndTime: { type: 'number', default: 0 }
                }
            },
            attendance: {
                type: 'object',
                properties: {
                    studentScan: { type: 'boolean', default: false },
                    autoSystem: { type: 'boolean', default: false }
                }
            },
            leave: {
                type: 'object',
                properties: {
                    enabled: { type: 'boolean', default: false },
                    leaveDeadline: { type: 'number', default: 0 }
                }
            },
            type: { type: 'string', default: 'fixed' }
        }
    }
};

// 检查教师空闲时间 Schema
export const checkTeacherFreeSchema = {
    tags: ['classes'],
    summary: '获取课程老师时候空闲',
    body: {
        type: 'object',
        properties: {
            times: { type: 'number' },
            recurrenceType: { type: 'string', default: 'weekly' },
            daily: {
                type: 'object',
                properties: {
                    startTime: { type: 'string' },
                    endTime: { type: 'string' }
                }
            },
            startDate: { type: 'number' },
            endDate: { type: 'number' },
            endType: { type: 'string', default: 'number_of_times' },
            weekdays: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        day: { type: 'number', default: 1 },
                        startTime: { type: 'string' },
                        endTime: { type: 'string' }
                    }
                }
            }
        }
    }
};

// 其他 Schema 定义...
export const addStudentsSchema = {
    tags: ['classes'],
    summary: '班级添加学员',
    params: {
        type: 'object',
        required: ['classesId'],
        properties: {
            classesId: { type: 'string' }
        }
    },
    body: {
        type: 'object',
        required: ['studentIds'],
        properties: {
            studentIds: {
                type: 'array',
                items: { type: 'string' }
            }
        }
    }
};

export const updateClassSchema = {
    tags: ['classes'],
    summary: '更新班级',
    params: {
        type: 'object',
        required: ['classesId'],
        properties: {
            classesId: { type: 'string' }
        }
    },
    body: {
        type: 'object',
        properties: {
            name: { type: 'string' },
            teacherId: { type: 'string' },
            courseId: { type: 'string', default: null },
            isReserve: { type: 'boolean' },
            appointmentStartTime: { type: 'string' },
            appointmentEndTime: { type: 'string' },
            isQRCodeAttendance: { type: 'boolean' },
            isAutoCheckIn: { type: 'boolean' },
            isOnLeave: { type: 'boolean' },
            leaveDeadline: { type: 'string' },
            maxStudentCount: { type: 'string' },
            isShowWeekCount: { type: 'boolean' },
            classRoomId: { type: 'string' },
            status: {
                type: 'string',
                default: 'active',
                enum: ['active', 'graduated', 'disbanded']
            }
        }
    }
};