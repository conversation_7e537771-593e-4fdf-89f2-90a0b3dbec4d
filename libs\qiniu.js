import qiniu from 'qiniu';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';

const qiniuConfig = {
  accessKey: process.env.QINIU_ACCESS_KEY,
  secretKey: process.env.QINIU_SECRET_KEY,
  bucket: process.env.QINIU_BUCKET,
  domain: process.env.QINIU_DOMAIN,
};


const mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
const config = new qiniu.conf.Config();
const bucketManager = new qiniu.rs.BucketManager(mac, config);
const formUploader = new qiniu.form_up.FormUploader(config);
const putExtra = new qiniu.form_up.PutExtra();


export default async function uploadToQiniu(imageUrl, key) {
  try {
    // 下载图片
    const response = await fetch(imageUrl);
    if (!response.ok) throw new Error('下载图片失败');
    const arrayBuffer = await response.arrayBuffer();
    // 将ArrayBuffer转换为Buffer
    const buffer = Buffer.from(arrayBuffer);

    // 上传到七牛云
    return new Promise((resolve, reject) => {
      const uploadToken = new qiniu.rs.PutPolicy({
        scope: qiniuConfig.bucket,
      }).uploadToken(mac);

      formUploader.put(
        uploadToken,
        key,
        buffer,
        putExtra,
        (err, body, info) => {
          if (err) {
            reject(new Error(`七牛云上传失败: ${err.message}`));
          } else if (info.statusCode !== 200) {
            reject(new Error(`七牛云上传失败: ${info.statusCode}`));
          } else {
            resolve(`${qiniuConfig.domain}/${body.key}`);
          }
        }
      );
    });
  } catch (error) {
    throw new Error(`图片转存失败: ${error.message}`);
  }
}


export async function uploadAvatar(file) {
  try {
    // 上传到七牛云
    const key = `avatar/${uuidv4()}`
    return new Promise((resolve, reject) => {
        const uploadToken = new qiniu.rs.PutPolicy({
          scope: qiniuConfig.bucket,
        }).uploadToken(mac);
  
        formUploader.put(
          uploadToken,
          key,
          file,
          putExtra,
          (err, body, info) => {
            if (err) {
              reject(new Error(`七牛云上传失败: ${err.message}`));
            } else if (info.statusCode !== 200) {
              reject(new Error(`七牛云上传失败: ${info.statusCode}`));
            } else {
              resolve(`${qiniuConfig.domain}/${body.key}`);
            }
          }
        );
      });
  } catch (error) {
    throw new Error(`图片转存失败: ${error.message}`);
  }
}

export { bucketManager, formUploader, putExtra };
