import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';

const prisma = new PrismaClient();

/**
 * 测试用户登录时token清除功能
 */
async function testTokenCleanup() {
    console.log('🧪 开始测试用户登录token清除功能...\n');

    const baseUrl = 'http://localhost:3000';
    const testAccount = 'orgadmin';
    const testPassword = '123456';

    try {
        // 1. 检查登录前的token数量
        console.log('📊 检查登录前的token状态...');
        const userBefore = await prisma.user.findUnique({
            where: { account: testAccount },
            include: {
                userTokens: true
            }
        });

        if (!userBefore) {
            console.log('❌ 测试用户不存在，请先运行种子脚本');
            return;
        }

        console.log(`用户ID: ${userBefore.id}`);
        console.log(`登录前token数量: ${userBefore.userTokens.length}`);

        // 2. 第一次登录
        console.log('\n🔐 执行第一次登录...');
        const loginResponse1 = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: testAccount,
                password: testPassword
            })
        });

        if (!loginResponse1.ok) {
            const errorText = await loginResponse1.text();
            console.log('❌ 第一次登录失败:', errorText);
            return;
        }

        const loginData1 = await loginResponse1.json();
        console.log('✅ 第一次登录成功');
        console.log(`Access Token: ${loginData1.data.accessToken.substring(0, 20)}...`);
        console.log(`Refresh Token: ${loginData1.data.refreshToken.substring(0, 20)}...`);

        // 3. 检查第一次登录后的token数量
        const userAfterLogin1 = await prisma.user.findUnique({
            where: { account: testAccount },
            include: {
                userTokens: true
            }
        });

        console.log(`第一次登录后token数量: ${userAfterLogin1.userTokens.length}`);

        // 4. 等待一秒，然后第二次登录
        console.log('\n⏳ 等待1秒后执行第二次登录...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        const loginResponse2 = await fetch(`${baseUrl}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: testAccount,
                password: testPassword
            })
        });

        if (!loginResponse2.ok) {
            const errorText = await loginResponse2.text();
            console.log('❌ 第二次登录失败:', errorText);
            return;
        }

        const loginData2 = await loginResponse2.json();
        console.log('✅ 第二次登录成功');
        console.log(`Access Token: ${loginData2.data.accessToken.substring(0, 20)}...`);
        console.log(`Refresh Token: ${loginData2.data.refreshToken.substring(0, 20)}...`);

        // 5. 检查第二次登录后的token数量
        const userAfterLogin2 = await prisma.user.findUnique({
            where: { account: testAccount },
            include: {
                userTokens: true
            }
        });

        console.log(`第二次登录后token数量: ${userAfterLogin2.userTokens.length}`);

        // 6. 验证第一次登录的token是否失效
        console.log('\n🔍 验证第一次登录的token是否失效...');
        const testResponse = await fetch(`${baseUrl}/user/menus`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${loginData1.data.accessToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (testResponse.ok) {
            console.log('❌ 第一次登录的token仍然有效（应该已失效）');
        } else {
            console.log('✅ 第一次登录的token已失效');
        }

        // 7. 验证第二次登录的token是否有效
        console.log('\n🔍 验证第二次登录的token是否有效...');
        const testResponse2 = await fetch(`${baseUrl}/user/menus`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${loginData2.data.accessToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (testResponse2.ok) {
            console.log('✅ 第二次登录的token有效');
        } else {
            console.log('❌ 第二次登录的token无效');
        }

        // 8. 检查token是否不同
        console.log('\n🔍 检查两次登录的token是否不同...');
        const tokensAreDifferent = loginData1.data.accessToken !== loginData2.data.accessToken;
        console.log(`Access Token不同: ${tokensAreDifferent ? '✅' : '❌'}`);
        
        const refreshTokensAreDifferent = loginData1.data.refreshToken !== loginData2.data.refreshToken;
        console.log(`Refresh Token不同: ${refreshTokensAreDifferent ? '✅' : '❌'}`);

        // 9. 总结
        console.log('\n📋 测试总结:');
        console.log(`- 登录前token数量: ${userBefore.userTokens.length}`);
        console.log(`- 第一次登录后token数量: ${userAfterLogin1.userTokens.length}`);
        console.log(`- 第二次登录后token数量: ${userAfterLogin2.userTokens.length}`);
        console.log(`- 旧token失效: ${!testResponse.ok ? '✅' : '❌'}`);
        console.log(`- 新token有效: ${testResponse2.ok ? '✅' : '❌'}`);
        console.log(`- Token不同: ${tokensAreDifferent && refreshTokensAreDifferent ? '✅' : '❌'}`);

        if (userAfterLogin2.userTokens.length === 1 && !testResponse.ok && testResponse2.ok && tokensAreDifferent) {
            console.log('\n🎉 Token清除功能测试通过！');
        } else {
            console.log('\n❌ Token清除功能测试失败！');
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    } finally {
        await prisma.$disconnect();
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        const response = await fetch('http://localhost:3000/health');
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 主函数
async function main() {
    const serverRunning = await checkServer();
    if (!serverRunning) {
        console.log('❌ 服务器未运行，请先启动服务器: npm run dev');
        return;
    }

    await testTokenCleanup();
}

main();
