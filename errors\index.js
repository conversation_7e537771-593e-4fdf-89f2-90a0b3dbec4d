// 错误统计和性能监控
const errorMetrics = {
    counts: {}, // 按错误类型统计错误数量
    lastErrors: {}, // 保存每种错误类型的最后几个实例
    maxErrorsPerType: 10, // 每种错误类型最多保存的错误数量
    totalErrors: 0, // 总错误数
    startTime: Date.now(), // 启动时间
};

// 记录错误
function recordError(error) {
    const errorType = error.errorCode || error.name || 'UNKNOWN_ERROR';

    // 初始化错误类型计数
    if (!errorMetrics.counts[errorType]) {
        errorMetrics.counts[errorType] = 0;
        errorMetrics.lastErrors[errorType] = [];
    }

    // 增加错误计数
    errorMetrics.counts[errorType]++;
    errorMetrics.totalErrors++;

    // 保存错误实例（限制数量）
    const errorInfo = {
        message: error.message,
        timestamp: new Date().toISOString(),
        stack: error.stack,
    };

    errorMetrics.lastErrors[errorType].unshift(errorInfo);
    if (errorMetrics.lastErrors[errorType].length > errorMetrics.maxErrorsPerType) {
        errorMetrics.lastErrors[errorType].pop();
    }
}

// 获取错误统计
export function getErrorMetrics() {
    return {
        ...errorMetrics,
        uptime: Math.floor((Date.now() - errorMetrics.startTime) / 1000),
    };
}

// 基础错误类
export class BaseError extends Error {
    constructor(message, statusCode, errorCode, details = null) {
        super(message);
        this.statusCode = statusCode;
        this.errorCode = errorCode || this.constructor.name;
        this.name = this.constructor.name;
        this.timestamp = new Date().toISOString();
        this.details = details;
        Error.captureStackTrace(this, this.constructor);

        // 记录错误统计
        recordError(this);
    }

    // 转换为标准响应格式
    toResponse() {
        return {
            success: false,
            error: {
                code: this.errorCode,
                message: this.message,
                status: this.statusCode,
                timestamp: this.timestamp,
                ...(this.details && { details: this.details })
            }
        };
    }
}

// 400 - 请求参数验证错误
export class VALIDATION_ERROR extends BaseError {
    constructor(message = '请求参数验证失败', details = null) {
        super(message, 400, 'VALIDATION_ERROR', details);
    }
}

// 401 - 未授权错误
export class UNAUTHORIZED_ERROR extends BaseError {
    constructor(message = '未授权访问', details = null) {
        super(message, 401, 'UNAUTHORIZED_ERROR', details);
    }
}

// 认证相关错误 (401)
export class AUTH_ERROR extends UNAUTHORIZED_ERROR {
    constructor(message = '认证失败', details = null) {
        super(message, 401, 'AUTH_ERROR', details);
    }
}

// 令牌过期错误 (401)
export class TOKEN_EXPIRED_ERROR extends UNAUTHORIZED_ERROR {
    constructor(message = '令牌已过期', details = null) {
        super(message, 401, 'TOKEN_EXPIRED_ERROR', details);
    }
}

// 令牌无效错误 (401)
export class TOKEN_INVALID_ERROR extends UNAUTHORIZED_ERROR {
    constructor(message = '令牌无效', details = null) {
        super(message, 401, 'TOKEN_INVALID_ERROR', details);
    }
}

// 403 - 禁止访问错误
export class FORBIDDEN_ERROR extends BaseError {
    constructor(message = '禁止访问', details = null) {
        super(message, 403, 'FORBIDDEN_ERROR', details);
    }
}

// 权限不足错误 (403)
export class PERMISSION_DENIED_ERROR extends FORBIDDEN_ERROR {
    constructor(message = '权限不足', details = null) {
        super(message, 403, 'PERMISSION_DENIED_ERROR', details);
    }
}

// 404 - 资源未找到错误
export class NOT_FOUND_ERROR extends BaseError {
    constructor(message = '资源未找到', details = null) {
        super(message, 404, 'NOT_FOUND_ERROR', details);
    }
}

// 409 - 资源冲突错误
export class CONFLICT_ERROR extends BaseError {
    constructor(message = '资源冲突', details = null) {
        super(message, 409, 'CONFLICT_ERROR', details);
    }
}

// 500 - 内部服务器错误
export class INTERNAL_ERROR extends BaseError {
    constructor(message = '服务器内部错误', details = null) {
        super(message, 500, 'INTERNAL_ERROR', details);
    }
}

// 错误处理中间件
export function errorHandler(error, req, reply) {
    // 记录请求信息（用于调试）
    const requestInfo = {
        url: req.url,
        method: req.method,
        ip: req.ip,
        headers: {
            ...req.headers,
            // 移除敏感信息
            authorization: req.headers.authorization ? '[REDACTED]' : undefined,
            cookie: req.headers.cookie ? '[REDACTED]' : undefined,
        },
        params: req.params,
        query: req.query,
    };

    // 如果是自定义错误类型
    if (error instanceof BaseError) {
        // 记录错误（如果BaseError构造函数中未记录）
        if (!error.timestamp) {
            recordError(error);
        }

        // 记录详细日志（仅针对服务器错误）
        if (error.statusCode >= 500) {
            console.error('Server error:', {
                error: {
                    message: error.message,
                    code: error.errorCode,
                    stack: error.stack,
                },
                request: requestInfo,
            });
        }

        // 发送标准错误响应
        reply.status(error.statusCode).send(error.toResponse());
        return;
    }

    // 处理 @fastify/error 创建的错误
    if (error.code && error.statusCode) {
        // 记录错误
        recordError(error);

        // 记录详细日志（仅针对服务器错误）
        if (error.statusCode >= 500) {
            console.error('Server error:', {
                error: {
                    message: error.message,
                    code: error.code,
                    stack: error.stack,
                },
                request: requestInfo,
            });
        }

        // 发送标准错误响应
        reply.status(error.statusCode).send({
            success: false,
            error: {
                code: error.code,
                message: error.message,
                status: error.statusCode,
                timestamp: new Date().toISOString(),
                ...(process.env.NODE_ENV === 'development' && {
                    stack: error.stack,
                }),
            }
        });
        return;
    }

    // 处理未知错误
    const unknownError = new INTERNAL_ERROR(
        error.message || '服务器内部错误',
        process.env.NODE_ENV === 'development' ? {
            originalError: error.message,
            stack: error.stack,
        } : null
    );

    // 记录详细日志
    console.error('Unexpected error:', {
        error: {
            message: error.message,
            stack: error.stack,
        },
        request: requestInfo,
    });

    // 发送标准错误响应
    reply.status(500).send(unknownError.toResponse());
}