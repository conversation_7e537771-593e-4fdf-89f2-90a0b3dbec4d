import fp from "fastify-plugin";
import swagger from "@fastify/swagger";
import swaggerUi from "@fastify/swagger-ui";

async function swaggerPlugin(fastify,options) {
    await fastify.register(swagger, {
        swagger: {
            info: {
                title: 'sixue API Documentation',
                description: 'API documentation for the sixue backend service',
                version: '0.0.1'
            },
            host: `127.0.0.1:${fastify.config.PORT}`,
            scheams: ['http'],
            consumes: ['application/json;charset=utf-8'],
            produces: ['application/json;charset=utf-8'],
            tags: [
                {name: 'user', description: '用户管理接口'},
            ]
        }
    })

    await fastify.register(swaggerUi, {
        routePrefix: '/docs',
        uiconfig: {
            docExpansion: 'list',
            deepLinking: false
        },
        uiHooks: {
            onRequest: function(request,reply,next) {
                next()
            },
            preHandler: function(request,reply,next) {
                next()
            }
        },
        staticCsp: true,
        transfromStaticCsp: (header) => header
    })
}

export default fp(swaggerPlugin, {
    name: 'swagger-plugin'
})