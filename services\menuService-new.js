import { AUTH_ERROR, INTERNAL_ERROR, NOT_FOUND_ERROR, FORBIDDEN_ERROR } from '../errors/index.js';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeQueryWithCache } from '../utils/db-utils.js';
import buildMenuTree from '../utils/buildMenuTree.js';
import { formatMenuData } from '../scripts/menuUtils.js';

/**
 * Menu Service
 * Contains business logic for menu-related operations
 */
export const menuService = {
    /**
     * Get user menus
     * @param {Object} params - Parameters
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Menu tree
     */
    async getUserMenus({ user, fastify }) {
        // Generate cache key
        const cacheKey = `user:menus:${user.id}`;
        const cacheTTL = 300; // Cache for 5 minutes

        // Clear cache for testing
        await fastify.redis.del(cacheKey);

        try {
            // Try to get from cache
            const cachedMenus = await fastify.redis.get(cacheKey);
            if (cachedMenus) {
                fastify.log.info(`Using cached menus for user ${user.id}`);
                return JSON.parse(cachedMenus);
            }

            fastify.log.info(`Building menus for user ${user.id}`);

            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Get user permission codes
                const permissionCodes = await this.getPermissionCodes({
                    client,
                    userId: user.id,
                    fastify
                });

                // Get all menus
                const allMenus = await this.fetchAllMenus({
                    client,
                    fastify
                });

                // Log for debugging
                fastify.log.info(`Fetched ${allMenus.length} menus from database`);

                // Filter menus by permissions
                const filteredMenus = this.filterMenusByPermissions(allMenus, permissionCodes);

                // Log for debugging
                fastify.log.info(`Filtered to ${filteredMenus.length} menus based on permissions`);

                // Process menu tree
                const menuTree = this.processMenuTree(filteredMenus);

                // Log for debugging
                fastify.log.info(`Built menu tree with ${menuTree.length} top-level items`);

                // Commit transaction
                await client.query('COMMIT');

                // Cache result
                const result = menuTree || [];
                await fastify.redis.set(cacheKey, JSON.stringify(result), 'EX', cacheTTL);

                return result;
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                fastify.log.error({
                    msg: 'Error in getUserMenus',
                    error: error.message,
                    stack: error.stack
                });
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            // If cache operation failed, try without cache
            if (!(error instanceof AUTH_ERROR || error instanceof INTERNAL_ERROR)) {
                fastify.log.error({
                    msg: 'Cache operation failed in getUserMenus',
                    error: error.message,
                    stack: error.stack
                });

                return this.getUserMenusNoCache({ user, fastify });
            }
            throw error;
        }
    },

    /**
     * Get user menus without cache (fallback)
     * @param {Object} params - Parameters
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Menu tree
     */
    async getUserMenusNoCache({ user, fastify }) {
        // Get database connection
        const client = await fastify.pg.connect();

        try {
            // Start transaction
            await client.query('BEGIN');

            // Get user permission codes
            const permissionCodes = await this.getPermissionCodes({
                client,
                userId: user.id,
                fastify
            });

            // Get all menus
            const allMenus = await this.fetchAllMenus({
                client,
                fastify
            });

            // Filter menus by permissions
            const filteredMenus = this.filterMenusByPermissions(allMenus, permissionCodes);

            // Process menu tree
            const menuTree = this.processMenuTree(filteredMenus);

            // Commit transaction
            await client.query('COMMIT');

            return menuTree || [];
        } catch (error) {
            // Rollback transaction
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    },

    /**
     * Get all menus
     * @param {Object} params - Parameters
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Menu tree
     */
    async getAllMenus({ user, fastify }) {
        // Generate cache key
        const cacheKey = `institution:menus:${user.institutionId}`;
        const cacheTTL = 300; // Cache for 5 minutes

        try {
            // Try to get from cache
            const cachedMenus = await fastify.redis.get(cacheKey);
            if (cachedMenus) {
                return JSON.parse(cachedMenus);
            }

            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Get institution admin
                const adminResult = await executeQuery(client, fastify,
                    `SELECT "userId"
                     FROM user_institution
                     WHERE "institutionId" = $1 AND "isAdmin" = true
                     LIMIT 1`,
                    [user.institutionId],
                    { queryName: 'getInstitutionAdmin' }
                );

                if (adminResult.rows.length === 0) {
                    throw new AUTH_ERROR('未找到机构管理员');
                }

                const adminUserId = adminResult.rows[0].userId;

                // Get admin permission codes
                const permissionCodes = await this.getPermissionCodes({
                    client,
                    userId: adminUserId,
                    fastify
                });

                // Get all menus
                const allMenus = await this.fetchAllMenus({
                    client,
                    fastify
                });

                // Filter menus by permissions
                const filteredMenus = this.filterMenusByPermissions(allMenus, permissionCodes);

                // Process menu tree
                const menuTree = this.processMenuTree(filteredMenus);

                // Commit transaction
                await client.query('COMMIT');

                // Cache result
                const result = menuTree || [];
                await fastify.redis.set(cacheKey, JSON.stringify(result), 'EX', cacheTTL);

                return result;
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof AUTH_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`获取菜单失败: ${error.message}`);
        }
    },

    /**
     * Get role menus
     * @param {Object} params - Parameters
     * @param {string} params.roleId - Role ID
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} Menu tree
     */
    async getRoleMenus({ roleId, user, fastify }) {
        // Generate cache key
        const cacheKey = `role:menus:${roleId}`;
        const cacheTTL = 300; // Cache for 5 minutes

        try {
            // Try to get from cache
            const cachedMenus = await fastify.redis.get(cacheKey);
            if (cachedMenus) {
                return JSON.parse(cachedMenus);
            }

            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if role exists
                const roleResult = await executeQuery(client, fastify,
                    `SELECT id FROM roles WHERE id = $1`,
                    [roleId],
                    { queryName: 'checkRoleExists' }
                );

                if (roleResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('角色不存在');
                }

                // Get role permission codes
                const rolePermissionsResult = await executeQuery(client, fastify,
                    `SELECT p.code
                     FROM permissions p
                     JOIN role_permissions rp ON p.id = rp."permissionId"
                     WHERE rp."roleId" = $1`,
                    [roleId],
                    { queryName: 'getRolePermissions' }
                );

                const permissionCodes = new Set(rolePermissionsResult.rows.map(row => row.code));

                // Get all menus
                const allMenus = await this.fetchAllMenus({
                    client,
                    fastify
                });

                // Filter menus by permissions
                const filteredMenus = this.filterMenusByPermissions(allMenus, permissionCodes);

                // Process menu tree
                const menuTree = this.processMenuTree(filteredMenus);

                // Commit transaction
                await client.query('COMMIT');

                // Cache result
                const result = menuTree || [];
                await fastify.redis.set(cacheKey, JSON.stringify(result), 'EX', cacheTTL);

                return result;
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`获取角色菜单失败: ${error.message}`);
        }
    },

    /**
     * Update role menus
     * @param {Object} params - Parameters
     * @param {string} params.roleId - Role ID
     * @param {Array} params.menuIds - Menu IDs
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async updateRoleMenus({ roleId, menuIds, user, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if role exists
                const roleResult = await executeQuery(client, fastify,
                    `SELECT id FROM roles WHERE id = $1`,
                    [roleId],
                    { queryName: 'checkRoleExists' }
                );

                if (roleResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('角色不存在');
                }

                // Get menu permissions
                const menuPermissionsResult = await executeQuery(client, fastify,
                    `SELECT id, "permissionId" FROM menus WHERE id = ANY($1)`,
                    [menuIds],
                    { queryName: 'getMenuPermissions' }
                );

                // Extract permission IDs
                const permissionIds = menuPermissionsResult.rows
                    .filter(row => row.permissionId)
                    .map(row => row.permissionId);

                // Delete existing role permissions
                await executeQuery(client, fastify,
                    `DELETE FROM role_permissions WHERE "roleId" = $1`,
                    [roleId],
                    { queryName: 'deleteRolePermissions' }
                );

                // Insert new role permissions
                if (permissionIds.length > 0) {
                    const values = permissionIds.map((permissionId, index) =>
                        `($1, $${index + 2}, $${permissionIds.length + 2})`
                    ).join(', ');

                    await executeQuery(client, fastify,
                        `INSERT INTO role_permissions ("roleId", "permissionId", id)
                         VALUES ${values}`,
                        [roleId, ...permissionIds, uuidv4()],
                        { queryName: 'insertRolePermissions' }
                    );
                }

                // Commit transaction
                await client.query('COMMIT');

                // Clear cache
                await fastify.redis.del(`role:menus:${roleId}`);
                await fastify.redis.del(`user:menus:*`);
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`更新角色菜单失败: ${error.message}`);
        }
    },

    /**
     * Create menu
     * @param {Object} params - Parameters
     * @param {Object} params.menuData - Menu data
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Object>} Created menu
     */
    async createMenu({ menuData, user, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Generate menu ID
                const menuId = uuidv4();

                // Extract menu data
                const {
                    name, path, icon, component, sort = 0, hidden = false,
                    permissionCode, parentId, redirect
                } = menuData;

                // Get permission ID if permission code is provided
                let permissionId = null;
                if (permissionCode) {
                    const permissionResult = await executeQuery(client, fastify,
                        `SELECT id FROM permissions WHERE code = $1`,
                        [permissionCode],
                        { queryName: 'getPermissionId' }
                    );

                    if (permissionResult.rows.length > 0) {
                        permissionId = permissionResult.rows[0].id;
                    }
                }

                // Insert menu
                const result = await executeQuery(client, fastify,
                    `INSERT INTO menus (
                        id, name, path, icon, component, sort, hidden,
                        "permissionId", "permissionCode", "parentId", redirect
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
                    ) RETURNING *`,
                    [
                        menuId, name, path, icon, component, sort, hidden,
                        permissionId, permissionCode, parentId, redirect
                    ],
                    { queryName: 'createMenu' }
                );

                // Commit transaction
                await client.query('COMMIT');

                // Clear cache
                await fastify.redis.del(`user:menus:*`);
                await fastify.redis.del(`institution:menus:*`);
                await fastify.redis.del(`role:menus:*`);

                // Format and return result
                return formatMenuData(result.rows[0]);
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            throw new INTERNAL_ERROR(`创建菜单失败: ${error.message}`);
        }
    },

    /**
     * Update menu
     * @param {Object} params - Parameters
     * @param {string} params.menuId - Menu ID
     * @param {Object} params.menuData - Menu data
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async updateMenu({ menuId, menuData, user, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if menu exists
                const menuResult = await executeQuery(client, fastify,
                    `SELECT id FROM menus WHERE id = $1`,
                    [menuId],
                    { queryName: 'checkMenuExists' }
                );

                if (menuResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('菜单不存在');
                }

                // Extract menu data
                const {
                    name, path, icon, component, sort, hidden,
                    permissionCode, parentId, redirect
                } = menuData;

                // Get permission ID if permission code is provided
                let permissionId = null;
                if (permissionCode) {
                    const permissionResult = await executeQuery(client, fastify,
                        `SELECT id FROM permissions WHERE code = $1`,
                        [permissionCode],
                        { queryName: 'getPermissionId' }
                    );

                    if (permissionResult.rows.length > 0) {
                        permissionId = permissionResult.rows[0].id;
                    }
                }

                // Update menu
                await executeQuery(client, fastify,
                    `UPDATE menus SET
                        name = COALESCE($1, name),
                        path = COALESCE($2, path),
                        icon = COALESCE($3, icon),
                        component = COALESCE($4, component),
                        sort = COALESCE($5, sort),
                        hidden = COALESCE($6, hidden),
                        "permissionId" = COALESCE($7, "permissionId"),
                        "permissionCode" = COALESCE($8, "permissionCode"),
                        "parentId" = COALESCE($9, "parentId"),
                        redirect = COALESCE($10, redirect)
                    WHERE id = $11`,
                    [
                        name, path, icon, component, sort, hidden,
                        permissionId, permissionCode, parentId, redirect,
                        menuId
                    ],
                    { queryName: 'updateMenu' }
                );

                // Commit transaction
                await client.query('COMMIT');

                // Clear cache
                await fastify.redis.del(`user:menus:*`);
                await fastify.redis.del(`institution:menus:*`);
                await fastify.redis.del(`role:menus:*`);
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`更新菜单失败: ${error.message}`);
        }
    },

    /**
     * Delete menu
     * @param {Object} params - Parameters
     * @param {string} params.menuId - Menu ID
     * @param {Object} params.user - User object
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<void>}
     */
    async deleteMenu({ menuId, user, fastify }) {
        try {
            // Get database connection
            const client = await fastify.pg.connect();

            try {
                // Start transaction
                await client.query('BEGIN');

                // Check if menu exists
                const menuResult = await executeQuery(client, fastify,
                    `SELECT id FROM menus WHERE id = $1`,
                    [menuId],
                    { queryName: 'checkMenuExists' }
                );

                if (menuResult.rows.length === 0) {
                    throw new NOT_FOUND_ERROR('菜单不存在');
                }

                // Check if menu has children
                const childrenResult = await executeQuery(client, fastify,
                    `SELECT id FROM menus WHERE "parentId" = $1`,
                    [menuId],
                    { queryName: 'checkMenuChildren' }
                );

                if (childrenResult.rows.length > 0) {
                    throw new FORBIDDEN_ERROR('菜单有子菜单，无法删除');
                }

                // Delete menu
                await executeQuery(client, fastify,
                    `DELETE FROM menus WHERE id = $1`,
                    [menuId],
                    { queryName: 'deleteMenu' }
                );

                // Commit transaction
                await client.query('COMMIT');

                // Clear cache
                await fastify.redis.del(`user:menus:*`);
                await fastify.redis.del(`institution:menus:*`);
                await fastify.redis.del(`role:menus:*`);
            } catch (error) {
                // Rollback transaction
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            if (error instanceof NOT_FOUND_ERROR || error instanceof FORBIDDEN_ERROR) {
                throw error;
            }
            throw new INTERNAL_ERROR(`删除菜单失败: ${error.message}`);
        }
    },

    /**
     * Get permission codes
     * @param {Object} params - Parameters
     * @param {Object} params.client - Database client
     * @param {string} params.userId - User ID
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Set>} Permission codes
     */
    async getPermissionCodes({ client, userId, fastify }) {
        // Log for debugging
        fastify.log.info(`Getting permission codes for user ${userId}`);

        const result = await executeQuery(client, fastify,
            `SELECT DISTINCT p.code
             FROM permissions p
             JOIN role_permissions rp ON p.id = rp."permissionId"
             JOIN roles r ON rp."roleId" = r.id
             JOIN user_roles ur ON r.id = ur."roleId"
             WHERE ur."userId" = $1
             ORDER BY p.code`,
            [userId],
            { queryName: 'getPermissionCodes' }
        );

        const permissionCodes = new Set(result.rows.map(row => row.code));

        // Log for debugging
        fastify.log.info(`Found ${permissionCodes.size} permission codes for user ${userId}`);

        // If no permissions found, add some default permissions to ensure menus are displayed
        if (permissionCodes.size === 0) {
            fastify.log.warn(`No permissions found for user ${userId}, adding default permissions`);

            // Add default permissions for basic access
            permissionCodes.add('dashboard:access');
            permissionCodes.add('system:access');
            permissionCodes.add('academic:access');
            permissionCodes.add('enrollment:access');
            permissionCodes.add('finance:access');
            permissionCodes.add('data:access');
            permissionCodes.add('org:access');
            permissionCodes.add('notification:access');
            permissionCodes.add('aitools:access');
        }

        return permissionCodes;
    },

    /**
     * Fetch all menus
     * @param {Object} params - Parameters
     * @param {Object} params.client - Database client
     * @param {Object} params.fastify - Fastify instance
     * @returns {Promise<Array>} All menus
     */
    async fetchAllMenus({ client, fastify }) {
        try {
            const result = await executeQuery(client, fastify,
                `SELECT id, name, path, icon, sort, hidden, "permissionId", "parentId",
                        component, "permissionCode", redirect
                 FROM menus
                 ORDER BY sort ASC`,
                [],
                {
                    queryName: 'fetchAllMenus',
                    timeout: 5000 // 5 seconds timeout
                }
            );

            // Log for debugging
            fastify.log.info(`Fetched ${result.rows.length} menus from database`);

            // If no menus found in database, check if we need to seed the database
            if (result.rows.length === 0) {
                fastify.log.warn('No menus found in database, this might indicate that the menu seed script needs to be run');

                // Return a minimal set of menus for development/testing
                return this.getDefaultMenus();
            }

            return result.rows;
        } catch (error) {
            fastify.log.error({
                msg: 'Error fetching menus',
                error: error.message,
                stack: error.stack
            });

            // Return default menus as fallback
            return this.getDefaultMenus();
        }
    },

    /**
     * Get default menus (fallback when no menus in database)
     * @returns {Array} Default menus
     */
    getDefaultMenus() {
        // Return a minimal set of menus for development/testing
        return [
            {
                id: '1',
                name: '仪表盘',
                path: '/dashboard',
                icon: 'ChartBarIcon',
                sort: 1,
                hidden: false,
                permissionCode: 'dashboard:access'
            },
            {
                id: '2',
                name: '系统管理',
                icon: 'CogIcon',
                sort: 10,
                hidden: false,
                permissionCode: 'system:access',
                children: [
                    {
                        id: '21',
                        name: '用户管理',
                        path: '/system/users',
                        icon: 'UserIcon',
                        sort: 1,
                        hidden: false,
                        parentId: '2',
                        permissionCode: 'system:user:view'
                    },
                    {
                        id: '22',
                        name: '角色管理',
                        path: '/system/roles',
                        icon: 'ShieldCheckIcon',
                        sort: 2,
                        hidden: false,
                        parentId: '2',
                        permissionCode: 'system:role:view'
                    },
                    {
                        id: '23',
                        name: '菜单管理',
                        path: '/system/menus',
                        icon: 'MenuIcon',
                        sort: 3,
                        hidden: false,
                        parentId: '2',
                        permissionCode: 'system:menu:view'
                    }
                ]
            }
        ];
    },

    /**
     * Filter menus by permissions
     * @param {Array} menus - Menus
     * @param {Set} permissionCodes - Permission codes
     * @returns {Array} Filtered menus
     */
    filterMenusByPermissions(menus, permissionCodes) {
        // If no menus, return empty array
        if (!menus || menus.length === 0) {
            return [];
        }

        // If no permission codes, return all menus (fallback for development/testing)
        if (!permissionCodes || permissionCodes.size === 0) {
            return menus;
        }

        return menus.filter(menu => {
            // Keep menu if it has no permission code or the user has the permission
            const hasPermission = !menu.permissionCode || permissionCodes.has(menu.permissionCode);

            // For parent menus, also check if the permission code is a prefix of any permission the user has
            if (!hasPermission && menu.permissionCode && !menu.parentId) {
                const prefix = menu.permissionCode.split(':')[0];
                for (const code of permissionCodes) {
                    if (code.startsWith(prefix + ':')) {
                        return true;
                    }
                }
            }

            return hasPermission;
        });
    },

    /**
     * Process menu tree
     * @param {Array} menus - Menus
     * @returns {Array} Menu tree
     */
    processMenuTree(menus) {
        // If no menus, return empty array
        if (!menus || menus.length === 0) {
            return [];
        }

        // Log for debugging
        console.log(`Processing menu tree with ${menus.length} items`);

        // Make a deep copy of the menus to avoid modifying the original
        const menusCopy = JSON.parse(JSON.stringify(menus));

        // Log parent-child relationships for debugging
        const parentChildMap = {};
        menusCopy.forEach(menu => {
            if (menu.parentId) {
                if (!parentChildMap[menu.parentId]) {
                    parentChildMap[menu.parentId] = [];
                }
                parentChildMap[menu.parentId].push(menu.id);
            }
        });

        console.log('Parent-child relationships:', JSON.stringify(parentChildMap, null, 2));

        // Build menu tree
        const menuTree = buildMenuTree(menusCopy);

        // Log for debugging
        console.log(`Built menu tree with ${menuTree.length} top-level items`);

        // Remove fields AFTER the tree is built
        const removeFields = ['component', 'permissionId', 'redirect', 'permissionCode'];
        // Note: We're NOT removing 'parentId' here anymore
        this.removeFieldsFromTree(menuTree, removeFields);

        // Sort by sort
        menuTree.sort((a, b) => {
            const sortA = typeof a.sort === 'number' ? a.sort : 0;
            const sortB = typeof b.sort === 'number' ? b.sort : 0;
            return sortA - sortB;
        });

        return menuTree;
    },

    /**
     * Remove fields from tree
     * @param {Array|Object} tree - Tree
     * @param {Array} removeFields - Fields to remove
     */
    removeFieldsFromTree(tree, removeFields) {
        if (Array.isArray(tree)) {
            tree.forEach(item => this.removeFieldsFromTree(item, removeFields));
        } else if (tree && typeof tree === 'object') {
            for (const key in tree) {
                if (removeFields.includes(key)) {
                    delete tree[key];
                } else if (typeof tree[key] === 'object') {
                    this.removeFieldsFromTree(tree[key], removeFields);
                }
            }
        }
    }
};

export default menuService;
