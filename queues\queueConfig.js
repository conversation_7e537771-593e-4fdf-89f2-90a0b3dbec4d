/**
 * Common configuration for BullMQ queues
 * This file provides shared configuration and utility functions for all queues
 */

import { redisConfig } from "../config/redis.js";

/**
 * Default queue options that can be used across all queues
 */
export const defaultQueueOptions = {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000
    },
    removeOnComplete: {
      age: 3600, // 1 hour
      count: 1000
    },
    removeOnFail: {
      age: 3600 * 24 * 7 // 7 days
    },
    lockDuration: 300000, // 5 minutes
    timeout: 300000, // 5 minutes
  },
  streams: {
    events: {
      maxLen: 10000
    }
  }
};

/**
 * Helper function to create a queue with error handling
 * @param {string} name - The name of the queue
 * @param {object} options - Queue options
 * @returns {Queue} - The created queue
 */
export function createQueue(name, options = {}) {
  const { Queue } = require('bullmq');
  
  // Merge default options with provided options
  const queueOptions = {
    ...defaultQueueOptions,
    ...options
  };
  
  // Create the queue
  const queue = new Queue(name, queueOptions);
  
  // Add error handling
  queue.on('error', (error) => {
    console.error(`Queue ${name} error:`, error);
  });
  
  queue.on('failed', (job, error) => {
    console.error(`Job ${job.id} in queue ${name} failed:`, error);
  });
  
  return queue;
}

/**
 * Helper function to add a job to a queue with standard options
 * @param {Queue} queue - The queue to add the job to
 * @param {string} jobName - The name of the job
 * @param {object} data - The job data
 * @param {object} options - Job options
 * @returns {Promise<Job>} - The created job
 */
export async function addJob(queue, jobName, data, options = {}) {
  try {
    return await queue.add(jobName, data, options);
  } catch (error) {
    console.error(`Error adding job ${jobName} to queue ${queue.name}:`, error);
    throw error;
  }
}
